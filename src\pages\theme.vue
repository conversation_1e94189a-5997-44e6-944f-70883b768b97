<template>
  <v-container fluid>
    <div class="pa-2">
      <v-switch
        v-model="$vuetify.theme.dark"
        :prepend-icon="$vuetify.theme.dark ? '$weatherNight' : '$weatherSunny'"
        label="color mode"
      />
    </div>
    <h1>TYPOGRAPHY</h1>
    <h1>COLOR THEME</h1>
    <h1>FONTS</h1>
    <div v-for="icon in icons" :key="icon">
      <p>{{ icon }}</p>
      <v-icon>{{ '$' + icon }}</v-icon>
    </div>
  </v-container>
</template>

<script lang="ts">
import { defineComponent, useContext } from '@nuxtjs/composition-api'
export default defineComponent({
  setup() {
    const { app } = useContext()
    const icons = Object.keys(app.vuetify?.preset.icons.values || {})
    return { icons }
  }
})
</script>
