<!-- eslint-disable camelcase -->
<script lang="ts">
import {
  computed,
  defineComponent,
  onBeforeMount,
  ref
} from '@vue/composition-api'
import { useRoute } from '@nuxtjs/composition-api'
import { useRepository, useWellcare<PERSON>pi } from '@wellcare/nuxt-module-data-layer'

export default defineComponent({
  layout: 'blank',
  setup() {
    const route = useRoute()
    const { post } = useWellcareApi()

    const error = ref<string | null>(null)

    const slug = computed<string>(
      () => (route.value.query?.slug as string) || ''
    )
    const ssoInternalRefreshToken = computed<string>(
      () => (route.value.query?.refresh_token as string) || ''
    )
    const encodedRedirectUrl = computed<string>(
      () => (route.value.query?.redirect_url as string) || ''
    )
    const idp = computed<string>(() => (route.value.query?.idp as string) || '')
    const redirectUrl = computed(() =>
      encodedRedirectUrl.value ? atob(encodedRedirectUrl.value) : ''
    )

    const {
      execute,
      onSuccess,
      onError,
      loading: isLoading
    } = useRepository<{
      results: {
        refresh_token: string
        access_token: string
        idp: string
      }
    }>({
      fetcher: (data: { refreshToken: string; idp: string }) => {
        return post({
          url: `/identity/token-sso/idp/${data.idp}`,
          data: {
            token: data.refreshToken
          }
        })
      },
      toastOnError: false,
      useFetch: false
    })

    const constructCheckoutUrl = () => {
      const currentDomain = window.location.origin
      const queryParams = route.value.query
      const filteredParams = Object.entries(queryParams)
        .filter(([key]) => key !== 'refresh_token' && key !== 'idp')
        .map(([key, value]) => `${key}=${encodeURIComponent(value as string)}`)
        .join('&')

      return `${currentDomain}/checkout/${slug.value}?${filteredParams}`
    }

    const redirectToCheckout = (refreshToken: string, idpValue: string) => {
      const targetUrl = constructCheckoutUrl()
      window.location.href = `${targetUrl}&refreshToken=${refreshToken}&idp=${idpValue}`
    }

    onSuccess((res) => {
      if (res.results?.refresh_token) {
        redirectToCheckout(
          res.results.refresh_token,
          res.results.idp || idp.value
        )
      } else {
        error.value = 'Refresh token not received'
      }
    })

    onError((err) => {
      console.error('Error during token exchange:', err)
      error.value = 'An error occurred during authentication. Please try again.'
    })

    const retry = () => {
      error.value = null
      if (slug.value && ssoInternalRefreshToken.value && idp.value) {
        execute({ refreshToken: ssoInternalRefreshToken.value, idp: idp.value })
      } else {
        error.value = 'Missing required parameters'
      }
    }

    const goBack = () => {
      if (redirectUrl.value) {
        window.location.href = redirectUrl.value
      } else {
        console.error('No redirect URL provided')
      }
    }

    onBeforeMount(() => {
      if (slug.value && ssoInternalRefreshToken.value && idp.value) {
        execute({ refreshToken: ssoInternalRefreshToken.value, idp: idp.value })
      } else {
        error.value = 'Missing required parameters'
      }
    })

    return {
      isLoading,
      error,
      retry,
      goBack
    }
  }
})
</script>

<template>
  <v-container class="fill-height" fluid>
    <v-row justify="center" align="center">
      <v-col cols="12" sm="8" md="6" lg="4" class="text-center">
        <v-card
          v-if="isLoading"
          :style="{
            background: 'transparent',
            boxShadow: 'none'
          }"
          flat
        >
          <v-card-text>
            <v-progress-circular indeterminate color="primary" />
            <p class="mt-4">Đang xử lý, vui lòng đợi...</p>
          </v-card-text>
        </v-card>
        <v-card
          v-else-if="error"
          flat
          :style="{
            background: 'transparent',
            boxShadow: 'none'
          }"
        >
          <v-card-text class="error--text">
            <p>{{ error }}</p>
            <v-btn color="error" class="mt-4 mr-2" @click="retry">
              Thử lại
            </v-btn>
            <v-btn color="primary" class="mt-4" @click="goBack">
              Quay lại
            </v-btn>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>
