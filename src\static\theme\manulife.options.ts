// Types
import { VuetifyPreset } from 'vuetify/types/services/presets'
import colors from 'vuetify/lib/util/colors'
const ManulifeOps: any = {
  theme:{
    dark: false,
    disable: false,
    default: 'light',
    options: {
      customProperties: true
    },
    themes: {
      dark: {
        primary: colors.purple.base,
        secondary: colors.grey.darken1,
        accent: colors.shades.black,
        error: colors.red.accent3,
        success: "#4CAF50",
        info: "#2196F3",
        warning: "#FB8C00",
      },
      light: {
        primary: "#34384B",
        secondary: "#00A758",
        accent: "#EC6453",
        error: colors.red.accent3,
        success: "#4CAF50",
        info: "#2196F3",
        warning: "#FB8C00",
      }
    }
  }
}

export default ManulifeOps
