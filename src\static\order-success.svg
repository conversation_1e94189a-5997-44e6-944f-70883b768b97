<svg width="192" height="168" viewBox="0 0 192 168" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_2515_3393)">
<ellipse cx="101" cy="115" rx="43" ry="23" fill="#009688" fill-opacity="0.3"/>
</g>
<path d="M144 69C144 92.7482 124.748 112 101 112C77.2518 112 58 92.7482 58 69C58 45.2518 77.2518 26 101 26C124.748 26 144 45.2518 144 69ZM122.663 52.712C121.089 51.1377 118.536 51.1377 116.962 52.712C116.924 52.75 116.888 52.7903 116.855 52.8326L98.191 76.615L86.9383 65.3623C85.364 63.788 82.8116 63.788 81.2373 65.3623C79.663 66.9366 79.663 69.4891 81.2373 71.0634L95.4619 85.288C97.0362 86.8623 99.5887 86.8623 101.163 85.288C101.198 85.253 101.231 85.216 101.262 85.1773L122.72 58.3547C124.237 56.7763 124.218 54.267 122.663 52.712Z" fill="url(#paint0_linear_2515_3393)"/>
<circle cx="151" cy="24" r="4" fill="#0ABEAD" fill-opacity="0.5"/>
<circle cx="37.5" cy="44.5" r="2.5" fill="#0ABEAD" fill-opacity="0.5"/>
<circle cx="127.5" cy="143.5" r="2.5" fill="#0ABEAD" fill-opacity="0.5"/>
<circle cx="185.5" cy="57.5" r="2.5" fill="#0ABEAD" fill-opacity="0.8"/>
<circle cx="30.5" cy="102.5" r="4.5" fill="#0ABEAD" fill-opacity="0.8"/>
<path d="M19 10L17.0011 17.7789L10 20L17.0011 22.2211L19 30L20.9989 22.2211L28 20L20.9989 17.7789L19 10Z" fill="#0ABEAD" fill-opacity="0.6"/>
<path d="M158 105L163.445 106.555L165 112L166.555 106.555L172 105L166.555 103.445L165 98L163.445 103.445L158 105Z" fill="#0ABEAD" fill-opacity="0.7"/>
<defs>
<filter id="filter0_f_2515_3393" x="28" y="62" width="146" height="106" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="15" result="effect1_foregroundBlur_2515_3393"/>
</filter>
<linearGradient id="paint0_linear_2515_3393" x1="101" y1="26" x2="101" y2="112" gradientUnits="userSpaceOnUse">
<stop stop-color="#009688" stop-opacity="0.67"/>
<stop offset="1" stop-color="#009688"/>
</linearGradient>
</defs>
</svg>
