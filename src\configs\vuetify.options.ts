// import { Framework, IconsOptions } from 'vuetify/types'
import { IconsOptions } from 'vuetify/types/services/icons'
import colors from 'vuetify/es5/util/colors'
import {
  mdiAlertOutline,
  mdiPhone,
  mdiVideo,
  mdiArrowExpand,
  mdiCancel,
  mdiCheck,
  mdiCheckAll,
  mdiCheckboxBlankOutline,
  mdiCheckboxMarkedOutline,
  mdiCheckCircleOutline,
  mdiChevronLeft,
  mdiChevronRight,
  mdiCircleSmall,
  mdiClose,
  mdiCommentAlertOutline,
  mdiDeleteForeverOutline,
  mdiEmoticonSadOutline,
  mdiFileCheckOutline,
  mdiFilePdfBox,
  mdiFormatListGroup,
  mdiInformationVariant,
  mdiLoading,
  mdiMenu,
  mdiMenuDown,
  mdiMinusBoxOutline,
  mdiMinusCircleOutline,
  mdiPageFirst,
  mdiPageLast,
  mdiPencilOutline,
  mdiPlusCircleOutline,
  mdiRadioboxBlank,
  mdiRadioboxMarked,
  mdiSort,
  mdiStar,
  mdiStarHalfFull,
  mdiStarOutline,
  mdiTruckFast,
  mdiUnfoldMoreHorizontal,
  mdiPencil,
  mdiReload,
  mdiCalendar,
  mdiCameraEnhanceOutline,
  mdiFileOutline,
  mdiVideoPlusOutline,
  mdiCheckboxMarkedCircle,
  mdiTimer,
  mdiAccount,
  mdiDoctor,
  mdiAccountPlus,
  mdiAccountSearch,
  mdiPackage,
  mdiMagnify,
  mdiGenderFemale,
  mdiGenderMale,
  mdiLogoutVariant,
  mdiCheckCircle,
  mdiNumeric9CircleOutline,
  mdiNumeric8CircleOutline,
  mdiNumeric7CircleOutline,
  mdiNumeric6CircleOutline,
  mdiNumeric5CircleOutline,
  mdiNumeric4CircleOutline,
  mdiNumeric3CircleOutline,
  mdiNumeric2CircleOutline,
  mdiNumeric1CircleOutline,
  mdiNumeric10CircleOutline,
  mdiNumeric0CircleOutline,
  mdiAlert,
  mdiCheckboxBlankCircleOutline,
  mdiArrowUpThick,
  mdiSealVariant,
  mdiTrashCanOutline,
  mdiBellRing,
  mdiAccountCircle,
  mdiArrowLeft,
  mdiChatOutline,
  mdiChevronDoubleDown,
  mdiDotsVertical,
  mdiEmoticon,
  mdiHeart,
  mdiMicrophone,
  mdiRestore,
  mdiSend,
  mdiStop,
  mdiImage,
  mdiPlusBox,
  mdiCamera,
  mdiWeatherSunny,
  mdiWeatherNight,
  mdiCash,
  mdiRecord,
  mdiCloseCircle,
  mdiTag,
  mdiFlare,
  mdiContentCopy,
  mdiTrashCan,
  mdiCircleOutline,
  mdiWalletOutline,
  mdiCreditCardOutline,
  mdiAlertCircle,
  mdiCircle,
  mdiPlay,
  mdiChatProcessingOutline,
  mdiForumOutline,
  mdiChevronDown,
  mdiComment,
  mdiWaveform,
  mdiCloudUploadOutline,
  mdiDownload,
  mdiChevronDoubleUp,
  mdiMessageText,
  mdiForum,
  mdiCameraOutline,
  mdiInformationOutline,
  mdiCrown,
  mdiLightningBolt,
  mdiHome,
  mdiCheckboxMarked,
  mdiCalendarClock
} from '@mdi/js'

export default {
  theme: {
    dark: false,
    default: false,
    disable: false,
    options: {
      customProperties: true
    },
    themes: {
      light: {
        primary: '#009688',
        secondary: colors.yellow.lighten5,
        accent: '#ff5722',
        error: '#f44336',
        warning: '#ffc107',
        info: '#2196f3',
        success: '#4caf50',
        onPrimary: '#fff'
      },
      dark: {
        primary: '#009688',
        secondary: '#ff9800',
        accent: '#ff5722',
        error: '#f44336',
        warning: '#ffc107',
        info: '#2196f3',
        success: '#4caf50'
      }
    }
  },
  customVariables: ['~/assets/variables.scss'],
  treeShake: true,
  icons: {
    iconfont: 'mdiSvg',
    values: {
      informationOutline: mdiInformationOutline,
      pencilOutline: mdiPencilOutline,
      cameraOutline: mdiCameraOutline,
      check: mdiCheck,
      trashCan: mdiTrashCanOutline,
      trashOutline: mdiTrashCanOutline,
      filePdfBox: mdiFilePdfBox,
      emoticonSad: mdiEmoticonSadOutline,
      sealVariant: mdiSealVariant,
      phone: mdiPhone,
      video: mdiVideo,
      arrowUpThick: mdiArrowUpThick,
      checkboxBlankCircle: mdiCheckboxBlankCircleOutline,
      fileCheckOutline: mdiFileCheckOutline,
      alert: mdiAlert,
      checkCircleOutline: mdiCheckCircleOutline,
      numeric0CircleOutline: mdiNumeric0CircleOutline,
      numeric10CircleOutline: mdiNumeric10CircleOutline,
      numeric1CircleOutline: mdiNumeric1CircleOutline,
      numeric2CircleOutline: mdiNumeric2CircleOutline,
      numeric3CircleOutline: mdiNumeric3CircleOutline,
      numeric4CircleOutline: mdiNumeric4CircleOutline,
      numeric5CircleOutline: mdiNumeric5CircleOutline,
      numeric6CircleOutline: mdiNumeric6CircleOutline,
      numeric7CircleOutline: mdiNumeric7CircleOutline,
      numeric8CircleOutline: mdiNumeric8CircleOutline,
      numeric9CircleOutline: mdiNumeric9CircleOutline,
      bellRing: mdiBellRing,
      checkCircle: mdiCheckCircle,
      complete: mdiCheck,
      cancel: mdiCancel,
      close: mdiClose,
      logout: mdiLogoutVariant,
      delete: mdiDeleteForeverOutline,
      clear: mdiClose,
      success: mdiCheckAll,
      information: mdiInformationVariant,
      warning: mdiCommentAlertOutline,
      error: mdiAlertOutline,
      genderMale: mdiGenderMale,
      genderFemale: mdiGenderFemale,
      magnify: mdiMagnify,
      prev: mdiChevronLeft,
      package: mdiPackage,
      accountSearch: mdiAccountSearch,
      accountPlus: mdiAccountPlus,
      doctor: mdiDoctor,
      account: mdiAccount,
      left: mdiChevronLeft,
      timer: mdiTimer,
      right: mdiChevronRight,
      next: mdiChevronRight,
      checkboxOn: mdiCheckboxMarkedOutline,
      checkboxOff: mdiCheckboxBlankOutline,
      checkboxIndeterminate: mdiMinusBoxOutline,
      delimiter: mdiCircleSmall,
      sort: mdiSort,
      checkboxMarkedCircle: mdiCheckboxMarkedCircle,
      star: mdiStar,
      videoPlus: mdiVideoPlusOutline,
      file: mdiFileOutline,
      expand: mdiArrowExpand,
      cameraEnhance: mdiCameraEnhanceOutline,
      calendar: mdiCalendar,
      menu: mdiMenu,
      reload: mdiReload,
      subgroup: mdiFormatListGroup,
      dropdown: mdiMenuDown,
      radioOn: mdiRadioboxMarked,
      radioOff: mdiRadioboxBlank,
      edit: mdiPencilOutline,
      pencil: mdiPencil,
      ratingEmpty: mdiStarOutline,
      ratingFull: mdiStar,
      ratingHalf: mdiStarHalfFull,
      loading: mdiLoading,
      first: mdiPageFirst,
      last: mdiPageLast,
      unfold: mdiUnfoldMoreHorizontal,
      // file: mdiFileDocumentOutline,
      plus: mdiPlusCircleOutline,
      minus: mdiMinusCircleOutline,
      delivery: mdiTruckFast,
      'account-circle': mdiAccountCircle,
      'arrow-left': mdiArrowLeft,
      'chat-outline': mdiChatOutline,
      'check-all': mdiCheckAll,
      'chevron-double-down': mdiChevronDoubleDown,
      'close-circle': mdiCloseCircle,
      'dots-vertical': mdiDotsVertical,
      'trash-can-outline': mdiTrashCanOutline,
      // file: mdiFileDocumentOutline,
      circle: mdiCircle,

      emoticon: mdiEmoticon,

      heart: mdiHeart,
      image: mdiImage,

      microphone: mdiMicrophone,
      restore: mdiRestore,
      send: mdiSend,
      stop: mdiStop,
      weatherNight: mdiWeatherNight,
      weatherSunny: mdiWeatherSunny,
      'plus-box': mdiPlusBox,
      camera: mdiCamera,
      cash: mdiCash,
      record: mdiRecord,
      tag: mdiTag,
      'chevron-right': mdiChevronRight,
      flare: mdiFlare,
      'content-copy': mdiContentCopy,
      trash: mdiTrashCan,
      male: mdiGenderMale,
      female: mdiGenderFemale,
      circleOutline: mdiCircleOutline,
      'wallet-outline': mdiWalletOutline,
      chervonLeft: mdiChevronLeft,
      chevronRight: mdiChevronRight,
      'check-circle': mdiCheckCircle,
      'credit-card-outline': mdiCreditCardOutline,
      'alert-circle': mdiAlertCircle,
      'circle-small': mdiCircleSmall,
      play: mdiPlay,
      'chat-processing-outline': mdiChatProcessingOutline,
      'forum-outline': mdiForumOutline,
      'chevron-down': mdiChevronDown,
      'bell-ring': mdiBellRing,
      comment: mdiComment,
      'chevron-left': mdiChevronLeft,
      waveform: mdiWaveform,
      dotsVertical: mdiDotsVertical,
      cloudUpload: mdiCloudUploadOutline,
      download: mdiDownload,
      doubleDown: mdiChevronDoubleDown,
      doubleUp: mdiChevronDoubleUp,
      messageText: mdiForum,
      crown: mdiCrown,
      lightningBolt: mdiLightningBolt,
      home: mdiHome,
      checkboxMarked: mdiCheckboxMarked,
      calendarClock: mdiCalendarClock
    }
  } as IconsOptions
}
