// Ref: https://github.com/nuxt-community/vuetify-module#customvariables
//
// The variables you want to modify
// $font-size-root: 20px;
// $body-font-family: "Quicksand";
// $heading-font-family: 'Quicksand';
// $font-size-root: 18px;
// @import '~vuetify/src/styles/styles.sass'
$body-font-family: 'Quicksand';
$font-family: 'Quicksand';
$heading-font-family: 'Quicksand';
// .v-application {
//     [class*='text-'] {
//         color: #36405a;
//         font-family: $font-family, sans-serif !important;
//     }
//     font-family: $font-family,
//     sans-serif !important;
// }
// $font-size-root: 18px;
@import '~vuetify/src/styles/styles.sass';
