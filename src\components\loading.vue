<template>
  <v-overlay
    class="w-loading-app"
    z-index="1000"
    tile
    :absolute="absolute"
    :value="loading"
  >
    <div class="d-flex flex-column align-center">
      <div class="loadingio-spinner-ripple">
        <div class="ripple-loading">
          <div></div>
          <div></div>
        </div>
      </div>
    </div>
  </v-overlay>
</template>

<script lang="ts">
import { Vue, Component, VModel, Prop } from "vue-property-decorator";

@Component({
  components: {},
})
export default class LoadingApp extends Vue {
  @VModel({ default: false }) loadingModel!: boolean;
  @Prop({ default: false }) absolute!: boolean;
  @Prop({ default: "1000", type: String }) zIndex!: string;
  loading: boolean = false;
  start() {
    this.loading = true;
  }
  finish() {
    this.loading = false;
  }
}
</script>

<style scoped>
@media screen and (min-width: 1264px) {
  .w-loading-app {
    left: 256px;
  }
}
@keyframes ripple-loading {
  0% {
    top: 96px;
    left: 96px;
    width: 0;
    height: 0;
    opacity: 1;
  }
  100% {
    top: 18px;
    left: 18px;
    width: 156px;
    height: 156px;
    opacity: 0;
  }
}

.ripple-loading div {
  position: absolute;
  border-width: 4px;
  border-style: solid;
  opacity: 1;
  border-radius: 50%;
  animation: ripple-loading 1s cubic-bezier(0, 0.2, 0.8, 1) infinite;
}

.ripple-loading div:nth-child(1) {
  border-color: #e90c59;
  animation-delay: 0s;
}

.ripple-loading div:nth-child(2) {
  border-color: #46dff0;
  animation-delay: -0.5s;
}

.loadingio-spinner-ripple {
  width: 200px;
  height: 200px;
  display: inline-block;
  overflow: hidden;
  background: transparent;
}

.ripple-loading {
  width: 100%;
  height: 100%;
  position: relative;
  transform: translateZ(0) scale(1);
  backface-visibility: hidden;
  transform-origin: 0 0;
  /* see note above */
}

.ripple-loading div {
  box-sizing: content-box;
}

/* generated by https://loading.io/ */
</style>
