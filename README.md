[![Storybook](https://cdn.jsdelivr.net/gh/storybookjs/brand@master/badge/badge-storybook.svg)](https://www.chromatic.com/builds?appId=60aa11584da065004914156c)
[![Drone](https://drone.wellcare.vn/api/badges/Wellcare/vue-3-starter/status.svg?ref=refs/heads/sandbox)](https://drone.wellcare.vn/Wellcare/vue-3-starter)

# Purpose

Boilerplate for app run with Vue3, NuxtJS and TailwindCSS. This also contains Wellcare design system.

# Usage

# Development

To start app in local development, copy `default.env` to create `.env` file and run:

```sh
yarn dev 
```

Example Postman APIs are accessible from `./docs/mocks/`. You can start a mock server locally from `http://127.0.0.1:4010` by running
```sh
yarn mock
```

To deploy [to Chromatic](https://www.chromatic.com/builds?appId=60aa11584da065004914156c) to review UI updates:

```sh
yarn chromatic
```

Or view storybook locally:

```sh
yarn storybook
```


# Workflow:

- All `sandbox` branch commmit will be deployed automatically on https://vue-3-starter.mhealthvn.com.
- To check if it already run the latest version, check https://vue-3-starter.mhealthvn.com/version.txt.
- If there is problem with the build, check the build workflow: https://drone.wellcare.vn/Wellcare/vue-3-starter
- Updated UI to be reviewed [on Chromatic](https://www.chromatic.com/builds?appId=60aa11584da065004914156c)

# Links:

- Figma: https://www.figma.com/file/wYgNRsFaK5aLpqdAPNhrL3/website-starter
- API Docs: https://api.mhealthvn.com/cms/openapi/#/public (only pay attention to public, it contains all APIs for a public website)

  
