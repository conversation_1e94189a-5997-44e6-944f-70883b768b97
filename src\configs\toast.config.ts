const toast = {
  position: 'top-center',
  iconPack: 'material', // set your iconPack, defaults to material. material|fontawesome|custom-class
  singleton: true,
  keepOnHover: false,
  duration: 1500,
  // duration: 1500,
  className: 'w-toast',
  register: [
    // Register custom toasts
    {
      name: 'appError',
      message: (payload) => {
        // if there is a message show it with the message
        return payload?.message || 'Đã có lỗi xảy ra'
      },
      options: {
        type: 'error',
        duration: 2500
      }
    },
    {
      name: 'appWarning',
      message: (payload) => {
        // if there is a message show it with the message
        return payload?.message || 'Đã có lỗi xảy ra'
      },
      options: {
        type: 'warning',
        duration: 1500,
        className: ['w-toast', 'w-warning-toast']
      }
    },
    {
      name: 'appInfo',
      message: (payload) => {
        // if there is a message show it with the message
        return payload?.message || 'Đã có lỗi xảy ra'
      },
      options: {
        type: 'success',
        duration: 2500,
        className: ['w-toast', 'w-info-toast']
      }
    }
  ]
}
export default toast
