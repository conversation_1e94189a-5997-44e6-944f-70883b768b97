<template>
  <v-app>
    <v-app-bar
      v-if="isMobile"
      app
      height="50"
      color="primary"
      class="onPrimary--text"
      ><div
        class="d-flex align-center justify-space-between"
        style="width: 100%"
      >
        <v-btn icon small color="onPrimary" @click="mainDrawer = true"
          ><v-icon>$menu</v-icon></v-btn
        ><span>{{ $t('Service Registration - Wellcare') }}</span>
        <v-badge
          :color="totalFeeds && totalFeeds !== 0 ? 'error' : 'transparent'"
          :content="totalFeeds"
          right
          bottom
          overlap
          offset-y="20"
        >
          <v-btn color="onPrimary" icon @click="openFeed">
            <v-icon>$bell-ring</v-icon>
            <div v-if="totalFeeds > 0" class="ring-animation"></div>
          </v-btn>
        </v-badge></div
    ></v-app-bar>
    <client-only>
      <a
        ref="phoneHref"
        href="tel:+84366905905"
        style="opacity: 0; pointer-events: none"
      ></a>
      <v-dialog v-model="signOutDialog" width="300">
        <v-card class="pa-3 text-center"
          ><div style="font-size: 18px" class="font-weight-bold">
            {{ $t('sign out') }}
          </div>
          <v-divider class="my-2" />
          <div>{{ $t('You are leaving...Are you sure ?') }}</div>
          <v-card-actions class="px-0">
            <div
              class="d-flex align-center justify-space-around"
              style="width: 100%"
            >
              <v-btn
                outlined
                tile
                color="error"
                width="35%"
                class="text-capitalize"
                @click="signOutDialog = false"
                >{{ $t('cancel') }}</v-btn
              >
              <v-btn
                color="primary"
                depressed
                tile
                width="35%"
                class="text-capitalize"
                @click="signOut"
                >{{ $t('confirm') }}</v-btn
              >
            </div>
          </v-card-actions>
        </v-card>
      </v-dialog>
      <w-drawer
        v-model="mainDrawer"
        class="primary"
        width="290"
        :stateless="!isMobile"
        app
        height="100%"
      >
        <w-drawer-header
          :user="user"
          :feed-config="feedConfig"
          :show-minimize-button="false"
          class="onPrimary--text"
          @markFeed="openChat"
          @signOut="signOutDialog = true"
        >
          <template #notification>
            <v-badge
              v-if="!isMobile"
              :color="totalFeeds && totalFeeds !== 0 ? 'error' : 'transparent'"
              :content="totalFeeds"
              left
              bottom
              overlap
            >
              <v-btn color="onPrimary" icon @click="openFeed">
                <v-icon>$bell-ring</v-icon>
                <div v-if="totalFeeds > 0" class="ring-animation"></div>
              </v-btn>
            </v-badge>
            <w-feed-context
              :key="user._id"
              style="
                opacity: 0 !important;
                pointer-events: none !important;
                position: fixed;
                top: 1000%;
              "
              :user-id="user._id"
              :private-key="feedConfig"
              :name="user.name"
              @updateFeeds="updateFeeds"
            />
          </template>
        </w-drawer-header>
        <v-divider style="border-color: silver" />
        <w-drawer-group-item
          :title="guildGroup.title"
          :items="guildGroup.items"
        />
        <w-drawer-group-item
          :title="policyGroup.title"
          :items="policyGroup.items"
        />
        <!-- <w-drawer-footer
          class="onPrimary--text"
          @phoneSupport="phoneHref.click()"
          @chatSupport="openChat"
        /> -->
      </w-drawer>
      <v-main id="checkout-inspired">
        <nuxt />
      </v-main>
      <w-drawer
        v-model="sideBar.state"
        stateless
        app
        right
        hide-overlay
        :width="$vuetify.breakpoint.mdAndUp ? '425px' : '100%'"
      >
        <v-toolbar
          v-if="sideBar.is !== 'w-chat-window'"
          :height="60"
          dense
          elevation="2"
        >
          <div class="d-flex align-center justify-start" style="gap: 15px">
            <v-btn
              elevation="0"
              icon
              width="35"
              height="35"
              @click="sideBar.state = false"
            >
              <v-icon color="black">$arrow-left</v-icon>
            </v-btn>
            <div class="font-weight-bold">{{ $t(sideBar.title) }}</div>
          </div>
        </v-toolbar>
        <component
          :is="sideBar.is"
          v-bind="sideBar.meta"
          @backNavigate="sideBar.state = false"
          @callAction="phoneHref.click()"
          @updateFeeds="updateFeeds"
          @markFeed="openChat"
        />
      </w-drawer>
    </client-only>
    <!-- <zalo-chatbot /> -->
  </v-app>
</template>

<script lang="ts">
import {
  defineComponent,
  useStore,
  computed,
  useContext,
  ref,
  reactive,
  watch
} from '@nuxtjs/composition-api'

export default defineComponent({
  middleware: ['authenRedirect', 'track', 'checkMembership'],
  setup() {
    const contents = {
      vi: {
        chooseDoctorsGuild: 'chon-bac-si-chuyen-gia',
        callDoctorGuild: 'goi-bac-si-online-chi-voi-3-buoc',
        faq: 'cau-hoi-thuong-gap',
        feePolicy: 'chinh-sach-phi-kham-tu-xa',
        conditionsPolicy: 'dieu-khoan-and-dieu-kien-su-dung',
        privacyPolicy: 'bao-mat-thong-tin'
      },
      en: {
        chooseDoctorsGuild: 'choose-doctors-and-specialists',
        callDoctorGuild: 'only-3-steps-to-book-a-teleconsultation',
        faq: 'frequently-asked-questions',
        feePolicy: 'service-charge',
        conditionsPolicy: 'terms-and-conditions',
        privacyPolicy: 'privacy-policy'
      }
    }
    const totalFeeds = ref(0)
    const signOutDialog = ref(false)
    const mainDrawer = ref(true)
    const phoneHref = ref()
    const sideBar = ref({
      state: false,
      is: null,
      meta: null,
      title: null
    })
    const { state, dispatch }: any = useStore()
    const { $config, app, $vuetify, i18n } = useContext()
    const locale = computed(() => i18n.locale)
    const { $user } = app
    const isMobile = computed(() => $vuetify.breakpoint.mdAndDown)
    const user = computed(() => state?.authen?.user)
    const feedConfig = computed(() => $config?.feedConfig)
    const guildGroup = reactive({
      title: 'guild',
      items: [
        {
          text: 'choose doctors, experts',
          action: () =>
            openContent(
              contents[locale.value].chooseDoctorsGuild,
              'choose doctors, experts'
            )
        },
        {
          text: 'call doctor guild',
          action: () =>
            openContent(
              contents[locale.value].callDoctorGuild,
              'call doctor guild'
            )
        },
        {
          text: 'faq',
          action: () => openContent(contents[locale.value].faq, 'faq')
        }
      ]
    })
    const policyGroup = reactive({
      title: 'policies',
      items: [
        {
          text: 'fee',
          action: () => openContent(contents[locale.value].feePolicy, 'fee')
        },
        {
          text: 'conditions',
          action: () =>
            openContent(contents[locale.value].conditionsPolicy, 'conditions')
        },
        {
          text: 'privacy',
          action: () =>
            openContent(contents[locale.value].privacyPolicy, 'privacy')
        }
      ]
    })
    const signOut = () => {
      dispatch('authen/signOut', { ctx: app })
    }
    const openContent = (slug: string, title: string) => {
      sideBar.value.state =
        sideBar.value.is === 'w-content-page-container' &&
        sideBar.value.meta.slug === slug
          ? !sideBar.value.state
          : true
      sideBar.value.is = 'w-content-page-container'
      sideBar.value.meta = {
        key: slug,
        slug,
        showTitle: false,
        site: 'khamtuxa.vn',
        locale: locale.value,
        class: 'context-item'
      }
      sideBar.value.title = title
    }
    const openChat = () => {
      sideBar.value.state =
        sideBar.value.is === 'w-chat-window' ? !sideBar.value.state : true
      sideBar.value.is = 'w-chat-window'
      sideBar.value.meta = {
        key: user.value._id + sideBar.value.is + sideBar.value.state,
        from: 'khamtuxa',
        user: user.value._id,
        room: user.value._id,
        class: 'w-chat-window'
      }
    }
    const openFeed = () => {
      sideBar.value.state =
        sideBar.value.is === 'w-feed-context' ? !sideBar.value.state : true
      sideBar.value.is = 'w-feed-context'
      sideBar.value.meta = {
        key: user.value._id,
        'user-id': user.value._id,
        'private-key': feedConfig.value,
        name: user.value.name,
        class: 'mx-auto context-item'
      }
      sideBar.value.title = 'notification'
    }
    const updateFeeds = (data: any) => {
      totalFeeds.value = data.feeds
    }
    watch(
      isMobile,
      () => {
        if (isMobile.value) mainDrawer.value = false
        else mainDrawer.value = true
      },
      { immediate: true }
    )
    return {
      user,
      feedConfig,
      phoneHref,
      mainDrawer,
      sideBar,
      signOut,
      guildGroup,
      policyGroup,
      signOutDialog,
      isMobile,
      openChat,
      openFeed,
      updateFeeds,
      totalFeeds
    }
  },
  head() {
    return {
      title: `Đăng ký dịch vụ - Wellcare`
    }
  }
})
</script>
<style scoped>
.sidebar {
  border-left: 1px solid silver;
}
.context-item {
  height: calc(100% - 60px) !important;
  overflow-x: hidden !important;
  overflow-y: auto !important;
}
@media only screen and (min-width: 769px) {
  .context-item::-webkit-scrollbar-track {
    border-radius: 10px;
    background-color: #f5f5f5;
  }
  .context-item::-webkit-scrollbar {
    width: 5px;
    background-color: #f5f5f5;
  }
  .context-item::-webkit-scrollbar-thumb {
    background-color: rgba(138, 135, 135, 0.8);
    border-radius: 10px;
  }
  .context-item::-webkit-scrollbar-thumb:hover {
    background-color: rgba(138, 135, 135);
  }
}
.ring-animation {
  position: absolute;
  width: 30px;
  height: 30px;
  border: 1px solid red;
  border-radius: 50%;
  animation: ring-animation infinite 1.6s linear;
}
@keyframes ring-animation {
  0% {
    border-width: 3px;
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    border-width: 2px;
    transform: scale(1.25);
    opacity: 0.25;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}
</style>
