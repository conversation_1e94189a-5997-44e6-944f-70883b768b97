import { NuxtConfig } from '@nuxt/types'
import AppConfig from './src/configs/app.config.json'
import { authenConfig } from './src/configs/authen.config'
import buildConfig from './src/configs/build.config'
import { checkoutConfig } from './src/configs/checkout.config'
import { feedConfig } from './src/configs/feed.config'
import { firebaseConfig } from './src/configs/firebase.config'
import { googleFontsConfig } from './src/configs/google-fonts.config'
import { gtmConfig } from './src/configs/gtm.config'
import HeadConfig from './src/configs/head.config.json'
import { i18nConfig } from './src/configs/i18n.config'
import toast from './src/configs/toast.config'
import { vuetifyConfig } from './src/configs/vuetify.config'
const dev = process.env.NODE_ENV === 'sandbox'
const env = process.env
const config: NuxtConfig = {
  ssr: false, // server side true | false
  dev,
  env: {
    NODE_ENV: process.env.NODE_ENV || 'sandbox',
    FIRE_ENV: process.env.FIRE_ENV || 'sandbox'
  },
  components: ['@components/global'],
  privateRuntimeConfig: {
    port: env.PORT,
    host: env.HOST,
    app: AppConfig
  },
  publicRuntimeConfig: {
    appName: env.APP_NAME,
    cdnHost: env.CDN_HOST,
    accountBaseUrl: env.ACCOUNT_BASE_URL,
    apiGatewayEndpoint: env.API_GATEWAY_ENDPOINT,
    endpoint: env.API_ENDPOINT,
    socketEndpoint: env.SOCKET_ENDPOINT,
    jwt: {
      appId: env.APP_ID,
      appSecret: env.APP_SECRET
    },
    payment: {
      gatewayId: env.PAYMENT_GATEWAY_ID,
      sources: env.PAYMENT_SOURCES
        ? env.PAYMENT_SOURCES.split(',')
        : ['ewallet', 'benefit'], // ewallet, benefit
      topupSources: env.PAYMENT_TOPUP_SOURCES
        ? env.PAYMENT_TOPUP_SOURCES.split(',')
        : [],
      voucherAllow: env.PAYMENT_VOUCHER_ALLOW === 'false' ? false : true,
      displayPrice: env.PAYMENT_DISPLAY_PRICE === 'false' ? false : true,
      bySponsorOnly: env.PAYMENT_BY_SPONSOR_ONLY === 'true' ? true : false,
      sponsoredBy: env.PAYMENT_SPONSORED_BY
        ? env.PAYMENT_SPONSORED_BY.split(',')
        : [],
      consultationLink: env.PAYMENT_CONSULTATION_LINK,
      userExperience: env.MANULIFE_PAYMENT_UX,
      byCardType: env.PAYMENT_BY_CARD_TYPE
        ? env.PAYMENT_BY_CARD_TYPE.split(',')
        : ['cash', 'sub']
    },
    checkout: checkoutConfig(process.env),
    feedConfig: feedConfig(process.env),
    doctorBaseUrl: env.DOCTOR_BASE_URL,
    patientBaseUrl: env.PATIENT_BASE_URL,
    phrBaseUrl: env.PHR_BASE_URL,
    merchantAppSecret: env.MERCHANT_APP_SECRET,
    topupSources: env.TOPUPSOURCES,
    paymentSources: env.PAYMENTSOURCES,
    organization: env.ORGANIZATION,
    paymentGatewayId: env.PAYMENT_GATEWAY_ID,
    transformCDN: env.TRANSFORM_CDN,
    'nuxt-module-chat': {
      botStatus: process.env.BOT_STATUS || 'Published'
    },
    'nuxt-module-data-layer': {
      baseURL: process.env.API_ENDPOINT,
      accountBaseURL: process.env.ACCOUNT_BASE_URL,
      xTenantId: 'wellcare'
    },
    'nuxt-module-media': {
      uploadEndpoint:
        process.env.MEDIA_UPLOAD_ENDPOINT || 'https://upload.mhealthvn.com'
    },
    fileEndpoint: process.env.FILE_ENDPOINT,
    apm: {
      serviceName: 'checkout-wellcare'
    },
    'nuxt-module-account': authenConfig(process.env)
  },
  head: HeadConfig,
  googleFonts: googleFontsConfig,
  // loading: '@/components/provider/card-info/loading.vue', // using 'false' if you dont want using default loading
  loading: '~/components/loading.vue',
  css: ['~/assets/styles.css', '~/assets/flags/flags.css'],
  styleResources: {
    scss: ['~/assets/variables.scss']
  },
  plugins: [
    { src: '~/plugins/utils.ts', mode: 'client' },
    // { src: '~/plugins/configs.ts' },
    '~/plugins/services.ts',
    '~/plugins/dayjs'
  ],
  modules: [
    '@wellcare/nuxt-module-data-layer',
    '@wellcare/vue-authen',
    'cookie-universal-nuxt',
    '@nuxtjs/axios',
    'nuxt-user-agent',
    // '@wellcare/nuxt-module-data-layer',
    [
      '@nuxtjs/gtm',
      {
        ...gtmConfig,
        enabled: !dev,
        debug: dev
      }
    ],
    ['@nuxtjs/robots', { UserAgent: '*', Disallow: '*' }],
    '@nuxtjs/i18n',
    '@nuxtjs/toast',
    '@nuxtjs/dayjs',
    ['@nuxtjs/firebase', firebaseConfig],
    [
      'nuxtjs-microsoft-clarity',
      {
        id: 'hx20gkjeq9'
      }
    ]
  ],
  buildModules: [
    [
      '@nuxt/typescript-build',
      {
        ignoreNotFoundWarnings: true
      }
    ],
    '@nuxtjs/vuetify',
    'nuxt-typed-vuex',
    // "@nuxtjs/device",
    '@nuxtjs/google-fonts',
    '@nuxtjs/composition-api/module',
    // '@wellcare/checkout-module',
    // '@wellcare/payment-module',
    ['@wellcare/nuxt-module-account', { prefix: 'w', level: 1 }],
    ['@wellcare/nuxt-module-checkout', checkoutConfig(process.env)],
    // ['@wellcare/nuxt-module-data-layer', { prefix: 'w', level: 1 }],
    ['@wellcare/vue-component', { prefix: 'w', level: 1 }],
    ['@wellcare/nuxt-module-elastic', { prefix: 'w', level: 5 }],
    ['@wellcare/nuxt-module-media', { prefix: 'w', level: 1 }],
    ['@wellcare/nuxt-module-feeds', { prefix: 'w', level: 1 }],
    ['@wellcare/nuxt-module-chat', { prefix: 'w', level: 3 }],
    ['@wellcare/nuxt-module-content', { prefix: 'w', level: 6 }],
    ['@wellcare/nuxt-module-phr', { prefix: 'w', level: 1 }]
    // ['nuxt-webpack-optimisations', webpackOptimzationOption]
  ],
  build: buildConfig,
  // router: {
  //   middleware: ['authenRedirect', 'track', 'checkMembership'],
  //   base: '/'
  // },
  srcDir: process.cwd() + '/src/',
  dir: {
    assets: 'assets',
    layouts: 'layouts',
    middleware: 'middleware',
    pages: 'pages',
    static: 'static',
    store: 'store'
  },
  // buildDir: '.nuxt/',

  server: {
    port: env.PORT,
    host: env.HOST
  },
  vuetify: vuetifyConfig,
  i18n: i18nConfig,
  loadingIndicator: {
    name: 'chasing-dots',
    color: '#009688',
    background: 'white'
  },
  toast
}

export default config
//  -------duccanhvipro-----------------

//         ,----,
//    ___.`      `,
//    `===  O     :
//      `'.      .'
//         )    (                   ,
//        /      \_________________/|
//       /                          |
//      |                           ;
//      |               _____       /
//      |      \       ______7    ,'
//      |       \    ______7     /
//       \       `-,____7      ,'
// ^~^~^~^`\                  /~^~^~^~^
//   ~^~^~^ `----------------' ~^~^~^
//  ~^~^~^~^~^^~^~^~^~^~^~^~^~^~^~^~
