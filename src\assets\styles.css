body.dialog-is-open {
  overflow-y: hidden !important;
}

rounded-lg::before {
  border-radius: 16;
}

.rounded-xl::before {
  border-radius: 24px;
}

div.v-card__title.custom-card {
  height: 64px !important;
}

.v-card.w-card,
.w-card {
  box-shadow: 0px 10px 10px -5px #0096880f !important;
}

.v-card.w-card:hover,
.w-card:hover {
  transform: scale(1.02);
  transition: all 0.2s ease-in-out;
  background-color: #0096881f;
}

.v-card.w-card.active,
.w-card.active {
  box-shadow: 0px 5px 10px rgb(0 0 0 / 5%),
    inset 0 0 0px 2px var(--v-primary-base) !important;
}

.v-card.w-card.active,
.w-card.active {
  box-shadow: 0px 5px 10px rgb(0 0 0 / 5%),
    inset 0 0 0px 2px var(--v-primary-base) !important;
}

.v-card.w-card::before,
.w-card::before {
  border-radius: 8px;
}

.w-icon-circle {
  position: relative;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid grey;
}

.w-icon-circle.w-icon-circle__active {
  display: flex;
  border-color: var(--v-primary-base);
}

.w-icon-circle__active::before {
  content: '';
  margin: auto;
  width: 10px;
  height: 10px;
  animation: easeout 0.8s;
  border-radius: 50%;
  background-color: var(--v-primary-base);
}

@keyframes easeout {
  0% {
    transform: scale(0.2);
  }

  50% {
    transform: scale(1.2);
  }

  100% {
    transform: scale(1);
  }
}

main.v-main {
  background-color: #f9f9f9;
}

footer.theme--light.v-footer {
  background-color: #fff;
}

.none-uppercase {
  text-transform: none !important;
}

.smoke-background {
  background-color: #efefef26 !important;
}

.custom-card {
  height: 2.4em !important;
  border-radius: unset !important;
}

.w-shadow {
  box-shadow: 0px 10px 10px -5px #0096880f !important;
}

.border-radius-dynamic {
  border-radius: max(0px, min(8px, calc((100vw - 4px - 100%) * 9999))) / 8px !important;
}

.w-toast {
  padding: 20px !important;
  justify-content: start !important;
  font-family: Quicksand, sans-serif;
  font-weight: 500 !important;
}

.w-warning-toast {
  background-color: #ffc107 !important;
}

.w-info-toast {
  background-color: #17a2b8 !important;
}

.w-toast-container {
}

@media only screen and (min-width: 600px) {
  .w-toast {
    border-radius: 10px !important;
    min-width: 300px !important;
    transform: translateY(-100px) !important;
  }
}

.shake-me {
  /* Start the shake animation and make the animation last for 0.5 seconds */
  animation: shake 0.5s;
  /* When the animation is finished, start again */
  animation-iteration-count: infinite;
}

@keyframes shake {
  0% {
    transform: translate(1px, 1px) rotate(0deg);
  }

  10% {
    transform: translate(-1px, -2px) rotate(-1deg);
  }

  20% {
    transform: translate(-3px, 0px) rotate(1deg);
  }

  30% {
    transform: translate(3px, 2px) rotate(0deg);
  }

  40% {
    transform: translate(1px, -1px) rotate(1deg);
  }

  50% {
    transform: translate(-1px, 2px) rotate(-1deg);
  }

  60% {
    transform: translate(-3px, 1px) rotate(0deg);
  }

  70% {
    transform: translate(3px, 1px) rotate(-1deg);
  }

  80% {
    transform: translate(-1px, -1px) rotate(1deg);
  }

  90% {
    transform: translate(1px, 2px) rotate(0deg);
  }

  100% {
    transform: translate(1px, -2px) rotate(-1deg);
  }
}

.feed {
  color: var(--v-onPrimary-base) !important;
}

.order-page * {
  font-family: 'Quicksand', sans-serif !important;
}

.text-20 {
  font-size: 20px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.text-23 {
  font-size: 23px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.text-24 {
  font-size: 24px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.text-22 {
  font-size: 22px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.text-18 {
  font-size: 18px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.text-16 {
  font-size: 16px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.text-14 {
  font-size: 14px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.text-36 {
  font-size: 36px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.text-34 {
  font-size: 34px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.text-25 {
  font-size: 25px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.text-26 {
  font-size: 26px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.premiere-button {
  background: #fbc02d !important;
  /* fallback for old browsers */
  background: -webkit-linear-gradient(to right, #ffee58, #fbc02d) !important;
  /* Chrome 10-25, Safari 5.1-6 */
  background: linear-gradient(to right, #ffee58, #fbc02d) !important;
  /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
}

.conceirge-button {
  background: #1e88e5 !important;
  /* fallback for old browsers */
  background: -webkit-linear-gradient(to right, #90caf9, #1e88e5) !important;
  /* Chrome 10-25, Safari 5.1-6 */
  background: linear-gradient(to right, #90caf9, #1e88e5) !important;
  /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
}

.w-chat-window {
  height: 100% !important;
}

@media only screen and (max-width: 425px) {
  .text-20 {
    font-size: 19px !important;
  }

  .text-23 {
    font-size: 22px !important;
  }

  .text-24 {
    font-size: 23px !important;
  }

  .text-22 {
    font-size: 21px !important;
  }

  .text-18 {
    font-size: 17px !important;
  }

  .text-16 {
    font-size: 15px !important;
  }

  .text-14 {
    font-size: 13px !important;
  }

  .text-36 {
    font-size: 35px !important;
  }

  .text-34 {
    font-size: 33px !important;
  }

  .text-25 {
    font-size: 24px !important;
  }

  .text-26 {
    font-size: 26px !important;
  }
}

.lg-toolbar {
  opacity: 1 !important;
}
.tracking-normal {
  letter-spacing: normal !important;
}

.w-full {
  width: 100% !important;
}
