<template>
  <v-container>
    <p>user agent</p>
    <p>{{ $ua }}</p>
    <p>cookies</p>
    <p>{{ $cookies }}</p>
    <p>{{ $fire }}</p>
    <p>toast: {{ $toast }}</p>
    <v-btn @click="$toast.global.appWarning()">toast</v-btn>
  </v-container>
</template>

<script lang="ts">
import { defineComponent } from '@nuxtjs/composition-api'

export default defineComponent({
  // eslint-disable-next-line vue/name-property-casing
  name: 'DebugPage',
  setup() {}
})
</script>
