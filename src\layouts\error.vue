<template>
  <v-app dark>
    <v-container
      class="d-flex justify-center align-center"
      style="height: 100%"
    >
      <div class="d-flex flex-column align-center justify-center">
        <h1 v-if="error.statusCode === 404">
          {{ pageNotFound }}
        </h1>
        <h1 v-else>
          {{ error.message }}
        </h1>
        <NuxtLink to="/">
          <v-btn color="primary" rounded>
            <v-icon left>$home</v-icon>
            Home
          </v-btn>
        </NuxtLink>
      </div>
    </v-container>
    <!-- <zalo-chatbot /> -->
  </v-app>
</template>

<script>
export default {
  layout: 'empty',
  props: {
    error: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      pageNotFound: '404 Not Found',
      otherError: 'An error occurred'
    }
  },
  head() {
    const title =
      this.error.statusCode === 404 ? this.pageNotFound : this.otherError
    return {
      title
    }
  }
}
</script>

<style scoped>
h1 {
  font-size: 20px;
}
</style>
