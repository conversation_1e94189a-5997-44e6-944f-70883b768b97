<template>
  <v-card
    flat
    color="transparent"
    style="width: 100%; border-bottom: 1px solid silver !important"
  >
    <template v-if="$vuetify.breakpoint.height > 765">
      <v-card-text
        class="white--text py-1 px-3 text-uppercase"
        style="font-size: 16px !important"
        >{{ $t('support') }}</v-card-text
      >
      <div class="d-flex flex-wrap align-center justify-space-around">
        <v-btn
          class="text-capitalize transparent onPrimary--text"
          depressed
          height="80"
          width="50%"
          @click="$emit('phoneSupport')"
        >
          <div
            class="d-flex flex-column justify-space-around align-center"
            style="height: 60px"
          >
            <v-icon size="28">$phone</v-icon>
            <div style="font-size: 14px">{{ $t('phone') }}</div>
          </div>
        </v-btn>
        <v-btn
          class="text-capitalize transparent onPrimary--text"
          depressed
          width="50%"
          height="80"
          @click="$emit('chatSupport')"
        >
          <div
            class="d-flex flex-column justify-space-around align-center"
            style="height: 60px"
          >
            <v-icon size="28">$forum-outline</v-icon>
            <div style="font-size: 14px">{{ $t('chat') }}</div>
          </div>
        </v-btn>
        <v-menu top offset-x>
          <template #activator="{ on, attrs }">
            <v-btn
              class="text-capitalize transparent onPrimary--text"
              height="80"
              width="50%"
              depressed
              v-bind="attrs"
              v-on="on"
            >
              <div
                class="d-flex flex-column justify-space-around align-center"
                style="height: 60px"
              >
                <v-icon size="28">$chat-processing-outline</v-icon>
                <div style="font-size: 14px">{{ $t('other') }}</div>
              </div>
            </v-btn>
          </template>

          <v-list class="pa-0">
            <template v-for="(item, index) in otherSupports">
              <a
                :key="'support method' + index"
                :href="item.href"
                target="_blank"
                style="text-decoration: none; color: black"
              >
                <div
                  class="d-flex align-center item px-3 py-2"
                  style="gap: 10px"
                >
                  <v-img
                    :src="item.image"
                    contain
                    max-width="40"
                    max-height="40"
                  />
                  <div>
                    <div style="font-size: 16px" class="font-weight-bold">
                      {{ item.title }}
                    </div>
                    <div style="font-size: 14px">{{ item.subtitle }}</div>
                  </div>
                </div>
              </a>
            </template>
          </v-list>
        </v-menu>
      </div>
    </template>
    <template v-else>
      <v-expansion-panels v-model="model" class="pa-0" accordion flat>
        <v-expansion-panel>
          <v-expansion-panel-header class="pa-0 primary white--text">
            <v-card-text
              class="white--text py-1 px-3 text-uppercase"
              style="font-size: 16px !important"
              >{{ $t('support') }}</v-card-text
            >
            <template #actions>
              <v-icon
                color="white"
                class="mr-2"
                :style="
                  model !== 0
                    ? 'transform: rotate(180deg)'
                    : 'transform: rotate(360deg)'
                "
              >
                $chevron-down
              </v-icon>
            </template>
          </v-expansion-panel-header>
          <v-expansion-panel-content class="pa-0 primary white--text">
            <div
              class="d-flex flex-wrap align-center justify-space-around pa-0"
            >
              <v-btn
                class="text-capitalize transparent onPrimary--text"
                depressed
                height="80"
                width="50%"
                @click="$emit('phoneSupport')"
              >
                <div
                  class="d-flex flex-column justify-space-around align-center"
                  style="height: 60px"
                >
                  <v-icon size="28">$phone</v-icon>
                  <div style="font-size: 14px">{{ $t('phone') }}</div>
                </div>
              </v-btn>
              <v-btn
                class="text-capitalize transparent onPrimary--text"
                depressed
                width="50%"
                height="80"
                @click="$emit('chatSupport')"
              >
                <div
                  class="d-flex flex-column justify-space-around align-center"
                  style="height: 60px"
                >
                  <v-icon size="28">$forum-outline</v-icon>
                  <div style="font-size: 14px">{{ $t('chat') }}</div>
                </div>
              </v-btn>
              <v-menu top offset-x>
                <template #activator="{ on, attrs }">
                  <v-btn
                    class="text-capitalize transparent onPrimary--text"
                    height="80"
                    width="50%"
                    depressed
                    v-bind="attrs"
                    v-on="on"
                  >
                    <div
                      class="d-flex flex-column justify-space-around align-center"
                      style="height: 60px"
                    >
                      <v-icon size="28">$chat-processing-outline</v-icon>
                      <div style="font-size: 14px">{{ $t('other') }}</div>
                    </div>
                  </v-btn>
                </template>

                <v-list class="pa-0">
                  <template v-for="(item, index) in otherSupports">
                    <a
                      :key="'support method' + index"
                      :href="item.href"
                      target="_blank"
                      style="text-decoration: none; color: black"
                    >
                      <div
                        class="d-flex align-center item px-3 py-2"
                        style="gap: 10px"
                      >
                        <v-img
                          :src="item.image"
                          contain
                          max-width="40"
                          max-height="40"
                        />
                        <div>
                          <div style="font-size: 16px" class="font-weight-bold">
                            {{ item.title }}
                          </div>
                          <div style="font-size: 14px">{{ item.subtitle }}</div>
                        </div>
                      </div>
                    </a>
                  </template>
                </v-list>
              </v-menu>
            </div>
          </v-expansion-panel-content>
        </v-expansion-panel>
      </v-expansion-panels>
    </template>
  </v-card>
</template>
<script lang="ts">
import { defineComponent, reactive, ref } from '@nuxtjs/composition-api'

export default defineComponent({
  setup() {
    const otherSupports = reactive([
      {
        title: 'Zalo',
        subtitle: '+84 366 905 905',
        image: '/checkout/zalo-icon.png',
        href: 'https://zalo.me/2727084330920973261'
      },
      {
        title: 'Viber',
        subtitle: '+84 366 905 905',
        image: '/checkout/viper-icon.png',
        href: 'viber://add?number=84366905905/'
      },
      {
        title: 'Whatsapp',
        subtitle: '+84 366 905 905',
        image: '/checkout/whatsapp-icon.png',
        href: 'https://wa.me/84366905905'
      }
    ])
    const model = ref(null)
    return { otherSupports, model }
  }
})
</script>
<style scoped>
.item {
  transition: 0.2s linear;
  cursor: pointer;
}
.item:hover {
  background-color: #ece7e7;
}
</style>
