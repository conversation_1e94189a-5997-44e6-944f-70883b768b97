import ESLintPlugin from 'eslint-webpack-plugin'
import webpack from 'webpack'
const ASSET_PATH = '/checkout/_nuxt/'

const config = {
  output: {
    publicPath: ASSET_PATH
  },
  publicPath: ASSET_PATH,
  html: {
    minify: {
      collapseBooleanAttributes: true,
      decodeEntities: true,
      minifyCSS: true,
      minifyJS: true,
      processConditionalComments: true,
      removeEmptyAttributes: true,
      removeRedundantAttributes: true,
      trimCustomFragments: true,
      useShortDoctype: true,
      preserveLineBreaks: false,
      collapseWhitespace: true
    }
  },
  transpile: [
    // '@wellcare/web-client',
    'vue-debounce-decorator',
    '@wellcare/vue-authen',
    '@wellcare/checkout-module',
    '@wellcare/payment-module'
  ],
  optimization: {
    splitChunks: {
      name: true
    }
  },
  extend(config, ctx) {
    // Run ESLint on save
    if (ctx.isDev && ctx.isClient && config.module) {
      config.plugins.push(
        new ESLintPlugin({
          extensions: ['vue', 'ts']
        })
      )
    }
    if (config.module) {
      config.module.rules.push(
        {
          test: /\.s(c|a)ss$/,
          exclude: /(node_modules)/,
          use: [
            'vue-style-loader',
            'css-loader',
            {
              loader: 'sass-loader',
              options: {
                implementation: require('sass'),
                indentedSyntax: true // optional
              }
            }
          ]
        },
        {
          test: /\.mjs$/,
          include: /node_modules/,
          type: 'javascript/auto',
          use: {
            loader: 'babel-loader',
            options: {
              presets: ['@babel/preset-env']
            }
          }
        },
        {
          test: /\.cjs$/,
          include: /node_modules/,
          type: 'javascript/auto',
          use: {
            loader: 'babel-loader',
            options: {
              presets: ['@babel/preset-env']
            }
          }
        }
      )
      // config.module.rules.push({
      //   include: /node_modules/,
      //   test: /\.mjs$/,
      //   type: 'javascript/auto'
      // })
      config.node = {
        fs: 'empty'
      }
    }
  },
  babel: {
    presets(env, [preset, options]) {
      return [['@nuxt/babel-preset-app', options]]
    },
    plugins: [
      ['@babel/plugin-proposal-private-methods', { loose: true }],
      ['@babel/plugin-proposal-private-property-in-object', { loose: true }]
    ]
  },
  plugins: [
    // This makes it possible for us to safely use env vars on our code
    new webpack.DefinePlugin({
      'process.env.ASSET_PATH': JSON.stringify(ASSET_PATH)
    })
  ]
}

export default config
