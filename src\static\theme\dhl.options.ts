import { Options } from "@nuxtjs/vuetify";
import colors from 'vuetify/lib/util/colors'
const DHLOps: Options = {
  theme:{
    dark: false,
    disable: false,
    default: false,
    options: {
      customProperties: true
    },
    themes: {
      dark: {
        primary: colors.purple.base,
        secondary: colors.grey.darken1,
        accent: colors.shades.black,
        error: colors.red.accent3,
        success: "#4CAF50",
        info: "#2196F3",
        warning: "#FB8C00",
      },
      light: {
        primary: "#cc0000",
        secondary: "#ffcc00",
        accent: "#EC6453",
        error: colors.red.accent3,
        success: "#4CAF50",
        info: "#2196F3",
        warning: "#FB8C00",
      }
    }
  }
}

export default DHLOps
