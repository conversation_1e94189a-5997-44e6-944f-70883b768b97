{"You are leaving...Are you sure ?": "You are leaving...Are you sure ?", "notification": "Notification", "guild": "guides", "policies": "policies", "choose doctors, experts": "Choose the right specialist", "call doctor guild": "Voice and video call", "faq": "FAQs", "privacy": "Privacy Policy", "conditions": "Terms of Use", "fee": "Fees", "1. lacking of the necessary information": "1. Lacking of the necessary information.", "1. the inquiry is having less information than what is needed.": "1. The inquiry is having less information than what is needed.", "100% satisfaction guarantee": "100% satisfaction guarantee", "2. the patient should make a voice or video call with our doctor for a proper diagnosis or come to the nearest medical center soon": "2. The patient should make a voice or video call with our doctor for a proper diagnosis or come to the nearest medical center soon.", "250 characters": "250 characters", "3. Confirmation": "3. Confirmation", "30 minutes after receiving the money, Wellcare will text you a confirmation including the link to the medical record where you could upload the images and videos. But it may take longer if you transfer manually through internet banking, depends on the time it arrives": "30 minutes after receiving the money, Wellcare will text you a confirmation including the link to the medical record where you could upload the images and videos. But it may take longer if you transfer manually through internet banking, depends on the time it arrives.", "Available to patient after doctor click complete": "Available to patient after doctor click 'Complete'", "By health record": "My Account", "Cannot upload!": "Cannot upload!", "Cellular Call or Internet Call (using Wellcare app) on schedule": "Cellular Call or Internet Call (using Wellcare app) on schedule", "Detailed description placeholder": "Describe chronologically from the beginning: symptoms, progress, treatment received, treatment effectiveness...", "Doctor and Wellcare reserve the right to decline, whereas:": "Doctors & Wellcare reserve the right to decline, if:", "I bought package": "My package", "Later": "Later", "Long Chau will call you for confirm this order": "<PERSON> will call you for confirm this order", "Master of psychology": "Master of psychology", "OTP confirmation": "OTP confirmation", "RECOMMENDED PRESCRIPTION": "RECOMMENDED PRESCRIPTION", "Satisfied": "satisfied", "Send patient": "Send patient", "Show Medical Record": "Show Medical Record", "Someone else": "Someone else", "Sponsored by": "Sponsored by", "Systolic": "Systolic", "The exact age and gender of the patient is advisable": "The exact age and gender of the patient is advisable", "Upload images and videos to your e-medical record shortly after the appointment is confirmed, because the doctor will read it thoroughly": "Upload images and videos to your e-medical record shortly after the appointment is confirmed, because the doctor will read it thoroughly", "Video Call using Wellcare app on schedule (you can always switch to Cellular Call once the internet is unstable)": "<p style=\"margin-bottom: 0px;\">Video Call on schedule through our mobile app (you can always switch to Cellular once the internet is down).</p>", "You can upload the images, video clip, audio and files using the short link in sms (or from mobile app) ONLY AFTER the payment was done and the appointment was confirmed.": "<p>You can upload the images, video clip, audio and files using the short link in sms (or from mobile app) <span style=\"\n    color: #ff5722;\n\">ONLY AFTER</span> the payment was done and the appointment was confirmed.</p>", "You don't have any package. Please purchase": "You don't have any package. Please purchase", "You don't have enough flexible benefits balance": "You don't have enough flexible benefits balance", "You have {quota} uses remained": "You have <br/>  <span style='font-size:18pt; font-weight:bold'>{quota}</span> uses remained", "a doctor of your choice": "A doctor of your choice", "about us": "About us", "accept": "Accept", "acceptable": "acceptable", "account": "Account", "account registered successfully": "Account registered successfully", "activate": "Activate", "activate voice call": "Activate voice call", "add": "Add", "add event": "Add event", "add feature": "Add feature", "add location": "Add location", "add notification": "Add notification", "add question": "Add question", "add relative": "Add relationship", "add time": "Add time", "add to prescription": "Add to prescription", "additional instruction": "Additional instruction", "address": "Address", "after": "After", "after consultation, doctor may send you diagnosis, recommended prescriptions or treatment notes": "After consultation, doctor may send you diagnosis, recommended prescriptions or treatment notes", "after payment done": "After payment done:", "after payment is done we will send you a confirmation through sms and the wellcare mobile app please open the e medical record link in that sms or the wellcare mobile app to as soon as possible provide more detailed health information connect with the doctor on time there is a call button see the note and suggested prescription after receiving the completion confirmation send a follow up question to doctor as needed within 24 hours": "<p>After payment done, please:<br>- Check your sms or the wellcare mobile app. There is your e-medical record<br>- Upload images & videos from the link in that sms or our wellcare mobile app asap, our doctor will check it out<br>- Touch the call button on your e-medical record ontime<br>- See the note and  suggested prescription, after receiving the completion confirmation<br>- Send a follow-up question to doctor as needed, within 24 hours.<br>Please note: We may cancel the appointment unless you provide all the necessary medical information as requested.</p>", "age": "Age", "agree": "Agree", "agree to continue": "Agree to continue", "all": "All", "all day": "All day", "all doctor": "All doctor", "all existing appointments will be rescheduled": "All existing appointments will be rescheduled", "all features have been hidden": "All features have been hidden", "all medical information is encrypted and secured stored in compliance with HIPPA": "All medical information is encrypted and secured stored in compliance with HIPPA", "all of cmi profits are dedicated to the children’s solidarity fund that pays for destitute vietnamese children to have cardiac surgery at the heart institute": "All of CMI’s profits are dedicated to the Children’s Solidarity Fund that pays for destitute Vietnamese children to have cardiac surgery at The Heart Institute.", "allow the patient to select": "Allow the patient to select", "also, you have right to cancel, see more": "Also, you have right to cancel, see more:", "amount": "Amount", "amount is": "Amount is", "amount to topup is": "Amount to topup is", "an error occurred!": "An error occurred!", "answer": "answer", "answer question": "Answer", "anytime": "Anytime", "anywhere": "Anywhere", "app version": "App version", "application": "Application", "apply": "Apply", "appointment at clinic": "Appointment at clinic", "appointment is confirmed": "Appointment is confirmed", "appointment time passed": "Appointment time passed", "appointment-general": "In-depth consultation ", "are you sure to remove it": "Are you sure to remove it", "are you sure you want to delete": "Are you sure you want to delete?", "articles": "Articles", "as-needed": "as needed", "ask a doctor": "ask a doctor", "ask a question": "Ask a question", "at a post office or viettel store, you make an interbank transfer. ask the cashier to input your mobile phone number (preferably the one you registered with wellcare) as the note of the transfer.": "<p style=\"\">- At a Post office or Viettel store, you make an interbank transfer. Ask the cashier to input your mobile phone number as the note of the transfer. Cashier will also print a receipt, please capture and send it to Wellcare's zalo/viber/whatsapp <a href=\"tel:***********\" target=\"_blank\">+***********</a> for a clarification.</p><p style=\"\">- 30 minutes after receiving the money, Wellcare will text you a confirmation including the link to the medical record where you could upload the images and videos.</p><p style=\"margin-bottom: 0px;\">- Call the doctor: From the link we sent or our mobile app, you  have two options: cellular call and internet call.</p>", "at appointment time, patient call doctor by phone or video": "At appointment time, patient call doctor by phone or video", "at convenience stores or supermarkets": "At convenience stores or supermarkets", "at least": "At least", "at least 1 question required": "At least 1 question required", "at the appointment time, open medical record link and press CALL button to connect with doctor": "At the appointment time, open medical record link and press CALL button to connect with doctor", "at time": "At", "atm card": "ATM card", "attachments": "Attachments", "attendtion": "Attention", "attention": "Attention", "audio": "Audio", "auto redirect after": "Auto redirect after", "auto saved": "Saved automatically", "available balance": "Available balance", "back": "Back", "back to home": "back to home", "back to homepage": "Back to homepage", "balance": "Balance", "bank card": "Bank card", "bank transfer": "Bank transfer", "bank transfer description": "Note of bank transfer must be your phone number", "basic package": "BASIC", "before proceeding with a short question, please note that:": "Before proceeding with a short question, please note that:", "book": "Book", "book a consultation": "Teleconsultation", "book appointment at doctor offices": "Book appointment at doctor offices", "book follow up": "Follow up  ", "book now": "Book now", "book online consultation with dr {doctor}, {slot} minutes phone or video call": "Book online consultation with dr {doctor}, {slot} minutes phone or video call", "brand guideline": "Brand guideline", "buccal": "buccal", "buy": "buy", "by day": "By day", "by month": "By month", "calendar": "Calendar", "call": "Call", "call ": "Call Now", "call 84 28 3622 6822 to schedule with doctor": "<p>Please text us through <PERSON><PERSON> <a href=\"https://zalo.me/***********\" target=\"_blank\">+84 366 905 905</a> to book a teleconsultation with Dr. %{doctor}</p>", "call again": "Call again", "call by phone": "Call by phone", "call by phone or by Internet": "Call directly to the system phone number or call via the Internet.", "call center": "Call center", "call credit": "Call credit", "call doctor": "Call", "call doctor from a number other than your setting one": "Input your phone number here", "call doctor guide": "Call doctor guide", "call doctor through": "Cellular call", "call doctor via Internet": "VOICE CALL by INTERNET", "call doctors as easily as your loved ones": "Call doctors as easily as your loved ones", "call doctors in three simple steps": "Call doctors in three simple steps", "call dr": "Call Dr", "call dr by other phone": "Call <PERSON> by other phone", "call ended": "Call ended", "call from phone number": "Call from phone number", "call patient via Internet": "VOICE CALL by INTERNET", "call pn": "Call patient", "call pn by other phone": "Call patient by another phone no. instead of...", "call pt": "Call PT", "call support": "Call support", "call time": "Call time", "call to get otp": "Call to get OTP", "callback": "Call back", "calling": "Calling...", "cancel": "Cancel", "cancel question": "<PERSON><PERSON> question", "cancelled": "Cancelled", "cannot return": "Cannot return", "capsule": "Capsule", "capture, upload images": "Upload images", "card code": "Card code", "cash": "Get the code to pay by cash", "cash at convenience stores": "Cash", "cash on delivery": "Cash on delivery", "categories": "Categories", "centre medical international": "Centre Medical International", "centre medical international (CMI) is an outpatient clinic fully equipped to provide international standard comprehensive and specialised medical services, all the physicians are french or vietnamese": "Centre Medical International (CMI) is an outpatient clinic fully equipped to provide international standard comprehensive and specialised medical services. All the physicians are French or Vietnamese.", "change": "Change", "change outgoing phone alert": "<PERSON><PERSON> tác này chỉ thay đổi số điện thoại gọi đi đối với cuộc tư vấn này, số điện thoại sổ khám của bạn sẽ không bị thay đổi", "change password": "Change password", "change phone number": "Change phone number", "charge this consultation": "Charge this consultation", "charged by minutes of consultation": "Charged by minutes of consultation", "check sms message": "Check sms message", "check your SMS message, prepare medical record and call doctor": "Check your SMS message, prepare medical record and call doctor", "check your sms or the mobile app for the comfirmation you will receive the response within 24 hours please again check your sms or the mobile app": "<p>After payment done:<br>- Check the Wellcare mobile app for the confirmation<br>- You will receive the response within 24 hours, through sms and on the mobile app<br>- We may refuse your question, if it is not properly made.</p>", "check your sms or the mobile app for the confirmation": "Check your SMS or the mobile app for the confirmation", "chief complaint": "Chief complaint", "child": "Child", "choose": "<PERSON><PERSON>", "choose a bank": "Choose a bank", "choose a calendar to synchronize": "Choose a calendar to synchronize", "choose a day and a timeslot to continue": "Choose a day and a timeslot to continue", "choose a doctor": "Choose a doctor", "choose a patient to continue": "Choose a patient to continue", "choose a time": "Choose a time", "choose account": "Choose account", "choose an appointment": "Choose an appointment", "choose an option to continue": "Choose an option to continue", "choose appointment time": "Choose appointment time", "choose at lease one day": "Choose at lease one day", "choose available slot": "Choose available slot", "choose consultation package": "Choose consultation package", "choose continue with proceed or switch to teleconsultation": "Choose continue with proceed or switch to teleconsultation", "choose doctor": "Choose doctor", "choose gender": "Choose gender", "choose package": "Choose package", "choose the amount to topup": "Choose the amount", "choose the duration": "Choose the duration", "choose the time frame": "Choose the time frame", "choose time": "Choose time", "choose type of communication": "Choose type of communication", "choose vendor": "Choose vendor", "choose your date of birth": "Choose your date of birth", "city": "City", "click": "Click", "close": "Close", "collapse": "Collapse", "come to one of the following stores": "Come to one of the following stores:", "coming soon": "coming soon", "company name": "Company name", "complete": "Complete", "completed": "Completed", "completed call": "Completed call", "conditions diagnosed or treated via telemedicine": "Conditions diagnosed or treated via telemedicine", "confirm": "Confirm", "confirm merge": "Confirm merge", "confirm payment": "Confirm payment", "confirm phone": "Confirm phone", "confirm step question": "to receive a medical form to fill with more information, relevant images & videos, and to have our physicians interpret all of your symptoms to you.", "confirmation": "Confirmation", "consult for": "Consult for", "consult for adult": "Consult for adult", "consult for kids": "Consult for Kids", "consult for mental health": "Consult for mental health", "consult with doctor on time": "Consult with doctor on time", "consult your doctor before changing or adjusting your dose": "Consult your doctor before changing or adjusting your dose", "consultation": "Consultation", "consultation by appointment": "Consultation by appointment", "consultation by appointment then followup within": "Consultation by appointment then followup within", "consultation by appointment within the timeslot specified": "Consultation by appointment within the timeslot specified", "consultation details": "Consultation details", "consultation hours": "Consultation hours", "consultation packages": "Your packages", "consultation questions": "Questions", "consultation request": "Consultation request", "consultation was cancelled by": "Consultation was cancelled by", "consultation with 7 days followup, suitable when you want to keep communicating with doctors until get well": "Consultation with 7 days followup, suitable when you want to keep communicating with doctors until get well", "consultation-question": "Ask doctor", "consultation-question-provider": "Ask doctor", "consultation-question-provider.private-doctor": "ASK DOCTOR", "consultations that have 4 or 5 start ratings": "Consultations that have 4 or 5 start ratings", "consultations that have detailed treatment notes, 16% have e-prescriptions": "Consultations that have detailed treatment notes, 16% require prescriptions", "consultations today": "Consultations today", "consulting duration": "Consulting duration", "contact": "Contact", "continue": "Continue", "continue question": "Proceed", "continue updating": "Continue updating", "convenience stores": "Convenience stores", "convenience stores or supermarkets": "Get the code to pay by cash", "convert flexible benefits": "Convert flexible benefits", "copied": "<PERSON>pied", "country": "Country", "coupon applied": "Coupon applied", "create": "Create", "create account": "Create account", "create consultation schedule": "Create consultation schedule", "create event": "Create event", "create new account": "Create new account", "create phr for me": "Create PHR for me", "create phr for my dependent": "Create PHR for my dependent", "created successfully": "Created successfully", "credit": "Credit", "credit card or debit card": "Credit card or Debit card", "cs": "CS", "current medication": "Current medication", "current password is invalid": "Current password is invalid", "current phone number": "Current phone: +{number}", "currently, Internet call is supported from IOS version 11 or above": "Currently, Internet call is supported from IOS version 11 or above", "customer support": "Customer support", "dans": "Within", "dashboard": "Dashboard", "data not affected when role is changed": "Data not affected when role is changed", "data of the two accounts (including relatives, medical records, profiles, transactions) will be merged": "Data of the two accounts (including relatives, medical records, profiles, transactions) will be merged", "date of birth": "Date of birth", "day": "Day", "days in week": "Days in week", "days old": "days old", "debit card description": "<p>- Within 30 minutes after receiving the payment, Wellcare will send you a text message to confirm, including the link to the medical record where you could upload the images and videos.</p><p style=\"margin-bottom:0px\">&nbsp;- To call the doctor, you can use the link we sent or our mobile app with two options: cellular call or internet call.</p>", "decline": "Decline", "default view": "Default view", "default_": "<PERSON><PERSON><PERSON>", "delivered": "Delivered", "delivery address": "Address", "delivery information": "Delivery Information", "delivery medication": "Drug delivery", "delivery note": "Note (optional)", "dental": "dental", "describe and upload images, video clips and files related to current medications, lab results, imaging results, doctor will review carefully your record and may request additional information": "Describe and upload images, video clips and files related to current medications, lab results, imaging results. Doctor will review carefully your record and may request additional information", "description": "Description", "detailed description": "Detailed description", "dhl telemedicine": "DHL Telemedicine", "diagnosis": "Diagnosis", "diagnosis.already_existed": "Diagnose is already existed", "diastolic": "diastolic", "disappointed": "disappointed", "discard change": "Discard change", "discount": "Discount", "distributed by long chau pharmacy": "Distributed by Long Chau Pharmacy", "district": "District", "do you want to cancel?": "Do you want to cancel?", "doctor": "Doctor", "doctor article": "Doctor Articles", "doctor does not support this": "Doctor does not support this", "doctor response time": "❗️ The doctor will respond within {time} hours", "doctor sending patient": "doctor sending patient", "doctors": "Doctors", "doctors and wellcare reserve the right to cancel the appointment when you decline to provide additional proper information so as to achieve optimal telehealth results. fee will be refunded to your wellcare account for later uses.": "Doctors and Wellcare reserve the right to cancel the appointment when you decline to provide additional proper information so as to achieve optimal telehealth results. Fee will be refunded to your Wellcare account for later uses.", "doctors answer": "Waiting for answer", "doctors are chosen based on: experience, expertise and service mindset": "Doctors are chosen based on: experience, expertise and service mindset", "doctors practice evidence based medicine, avoid overuse of drugs and labs, aim at treatment plans that are safe and minimally invasive": "Doctors practice evidence based medicine, avoid overuse of drugs and labs, aim at treatment plans that are safe and minimally invasive", "document": "Document", "does not support uploading video longer than 1 minute": "Does not support uploading video longer than 1 minute", "don't receive the otp?": "Don't receive the otp?", "done": "Done", "dont show again": "Dont show again", "dont use email": "Don't use email", "download at": "Download at", "download now": "Download now", "download app": "Download app", "dr": "Dr.", "drop": "Drop", "duration": "Duration", "e-medical record": "E-medical record", "each time use": "Each time use", "earliest": "Calendar", "easy access to your preferred doctor": "Easy access to your preferred doctor", "easy payment": "Easy payment", "edit": "Edit", "edit event": "Edit event", "edit question": "Edit question", "education": "Education", "education messages have been sent to patients to help develop healthy living habits": "Education messages have been sent to patients to help develop healthy living habits", "effective": "Effective", "email": "Email", "email content not found": "Email content not found", "email <EMAIL> or sms +84 366 905 905: name, phone, address to have card delivered. If you already have a card, scratch and enter code:": "Email <EMAIL> or sms +84 366 905 905: name, phone, address to have card delivered. If you already have a card, scratch and enter code:", "email is invalid": "<PERSON><PERSON> is invalid", "email not validated": "Email not validated", "email us or message to hotline +84 366 905 905 your NAME, PHONE, ADDRESS for home delivery": "<p>-Email us or message to hotline +84 366 905 905 your NAME, PHONE, ADDRESS for home delivery.</p><p style=\"margin-bottom:0px\">&nbsp;- If you already have a code, please key in:</p>", "employeeId": "Employee Id", "employeeNumber": "Employee Number", "empty search": "No matching results", "end": "End", "end date": "End date", "end of list": "End of List", "end time": "End time", "engage with your doctor for": "Engage with your doctor for", "engage with your doctor for 12 months, charged by minutes of consultation, suitable if you seek regular help": "Engage with your doctor for 12 months, charged by minutes of consultation, suitable if you seek regular help", "engage with your doctor for 12 months, suitable if you seek regular help": "Engage with your doctor for 12 months, suitable if you seek regular help", "enter a comment on the consultation, the doctor and the wellcare system": "Enter a comment on the consultation, the doctor and the Wellcare system", "enter a password": "Enter a password", "enter a valid amount of money": "Enter a valid amount of money", "enter account phone": "Please enter the phone number of your examination book", "enter another amount": "Enter another amount", "enter card code": "Enter card code", "enter chief complaint": "Enter chief complaint...", "enter code": "Enter code", "enter code to continue": "Enter code to continue", "enter current password": "Enter current password", "enter current password to continue": "Enter current password to continue", "enter gift code": "Enter gift code", "enter new password": "Enter new password", "enter otp": "Enter OTP", "enter otp to continue": "Enter OTP to continue", "enter password": "Enter password", "enter question to continue": "Enter question to continue", "enter symptoms": "Enter symptoms...", "enter the promo code": "Enter the promo code", "enter title": "Enter title", "enter your email": "Enter your email", "enter your password": "Welcome back, enter your password to continue", "enter your phone number": "Enter your phone number", "even better than physical visit": "Even better than physical visit", "event": "Event", "every-01-hour-as-needed": "every 1 hour as needed", "every-02-hours-as-needed": "every 2 hours as needed", "every-03-hours-as-needed": "every 3 hours as needed", "every-10-minutes-as-needed": "every 10 minutes as needed", "every-20-minutes-as-needed": "every 20 minutes as needed", "every-30-minutes-as-needed": "every 30 minutes as needed", "every-4-6-hour-as-needed": "every 4 - 6 hours as needed", "every-other-day": "every other day", "evidence based medicine": "Evidence based medicine", "excellent": "excellent", "expand": "Expand", "experience": "Experience", "expiration date": "Expiration date", "explore": "Explore", "external": "external", "eye-drops": "eye drops", "favorite provider": "Favorite doctors", "featured pediatricians": "Featured pediatricians", "fee policy": "Fee policy", "female": "Female", "fill out all required information to continue": "Fill out all required information to continue", "filled by doctor": "Filled by doctor", "filled by dr": "Filled by DR", "filled by patient": "Filled by patient", "filled by pt": "Filled by PT", "filter": "Filter", "filter condition": "Filter condition", "find momo topup locations": "Find momo topup locations", "find momo topup locations (see Guide below)": "Find a momo top-up location (below)", "five-times-a-day": "five times a day", "follow up": "Follow up", "follow-up": "Follow up  ", "for a period": "For a period", "for any extra payment that out of manulife free of charge services within 30 minutes after receiving the payment wellcare will send you a text message to confirm including the link to the medical record where you could upload the images and videos it may take longer if you transfer manually through internet banking depends on the time it arrives": "For any extra payment that out of Manulife free-of-charge services: Within 30 minutes after receiving the payment, Wellcare will send you a text message to confirm, including the link to the medical record where you could upload the images and videos. It may take longer if you transfer manually through internet banking, depends on the time it arrives.", "for assistance": "For assistance", "for me": "For me", "for my dependent": "For my dependent", "for quick support please copy or screenshot the error message and send us": "For quick support please copy or screenshot the error message and send us", "for someone else": "For someone else", "for this consultation": "for this consultation", "for this transaction only": "for this transaction only", "for this transaction or more for later uses": "for this transaction or more for later uses", "forgot password": "Forgot password", "four-times-a-day": "four times a day", "free": "Free", "from": "From", "from date": "From date", "from here": "From here", "frustrated": "frustrated", "full refund to your Wellcare’s account when declined": "Full <span style=\"font-weight:700\">refund</span> to your Wellcare’s account when declined", "fullname": "Full name", "fully booked": "Fully booked", "further instruction": "Further instruction", "gender": "Gender", "get well anywhere": "Get well anywhere", "group doctors": "Group Doctors", "growth chart": "Growth chart", "guest": "Guest", "guest permission": "Guest permission", "guide": "Guide", "have GotIt voucher/coupon?": "Have GotIt voucher/coupon?", "have a gift code?": "Have a gift code?", "have an account yet?": "Have an account yet?", "headCircumference": "Head Circumferece", "height": "Height", "hello": "Hello", "here": "Here", "hi": "Hi", "history": "History", "hold and drag to change order": "Hold and drag to change order", "holiday": "Holiday", "holiday surcharge": "Holiday surcharge", "home-treatment-f0": "Home Treatment", "homepage": "Homepage", "how would you rate the doctor?": "How would you rate the doctor?", "how would you rate wellcare?": "How would you rate wellcare?", "i agree to receive important notifications regarding appointments and doctor consultations": "I agree to receive important notifications regarding appointments and doctor consultations", "i already know that in case i worry, i definitely could ask wellcare": "I already know that in case I worry, I definitely could ask Wellcare +*********** (Zalo/Viber/Whatsapp) to request urgent advice from doctors prior to the appointment time.", "if there is anything you believe we should improve to meet your expectation please tell us": "If there is anything you believe we should improve to meet your expectation, please tell us!", "image": "Image", "images and video": "Images and video", "imaging results": "Imaging", "in account": "In account", "in-both-eyes": "in both eyes", "in-the-left-eye": "in the left eye", "in-the-right-eye": "in the right eye", "incoming call": "Incoming call", "inconsultation": "In-consultation", "indepth": "Indepth", "indepth consultation": "indepth consultation", "indepth consultation, without waiting or travelling": "Indepth consultation, without waiting or travelling", "information relatives": "Information relatives", "inhalation": "inhalation", "input doctor name": "Input doctor name", "input specialty": "Input specialty", "insert here": "Insert here", "install wellcare app and connect to our doctors in multiple ways": "Install Wellcare app and connect to our doctors in multiple ways", "instead of": "Instead of", "instruction step": "Instruction", "intramuscularly": "Intramuscularly", "intravenous": "intravenous", "introduction": "Introduction", "introductory video": "Introductory video", "invalid date": "invalid date", "invalid date of birth": "Invalid date of birth", "invalid email": "Invalid email", "invalid otp": "Invalid OTP", "invalid password": "Invalid password", "invalid phone": "Invalid phone", "invalid time": "invalid time", "invite": "Invite", "invoice information": "Invoice information", "is having a pending consultation with": "is having a pending consultation with", "is no longer available": "is no longer available", "its not you, its us. go back and try again": "It's not you, it's us. Go back and try again", "just now": "Just now", "keep it short & simple, straight to the point (maximum 250 characters).": "Keep it short & simple, straight to the point (maximum 250 characters).", "la0208 lexington office, 67 mai chi tho, d2, hcmc": "LA0208 Lexington Office, 67 Mai Chi Tho, District 2, Ho Chi Minh City", "lab results": "Lab results", "language": "Language", "later": "Later", "licensed ********** by DPI on 8 May 2015": "Licensed ********** by DPI on 8 May 2015", "limited": "limited", "list of doctors": "Doctors", "list of specialties": "Specialties", "loading": "Loading...", "local bank card": "Credit card or debit card", "locked": "Locked", "log in to continue with your saved data": "Log in to continue with your saved data", "login": "<PERSON><PERSON>", "login by": "Login by", "login now": "Login now", "logout": "Log out", "logout from current device": "Logout from current device", "made the call": "{name} made the call", "make a list of concerns but keep it short and focused. Each one is a question.": "Make a list of concerns but keep it short and focused. Each one is a question.", "male": "Male", "mannual instruction": "Mannual instruction", "manual instruction": "Manual instruction", "max": "Max", "maximum 50 characters": "Maximum 50 characters", "me": "Me", "medical assistant": "Medical assistant", "medical record": "Medical record", "medical record link and guide has been sent to your phone": "Medical record link and guide has been sent to your phone", "medical records have just been updated": "Medical records have just been updated", "medication": "Medication", "medium-phone": "Voice call", "medium-video": "Video call", "meet our pediatricians": "Meet our pediatricians", "membership card": "Membership", "message": "Message", "minimum": "Minimum", "minimum 10 characters": "Minimum 10 characters", "minimum 25 characters": "Minimum 25 characters", "minimum topup 20,000đ": "Minimum topup 20,000đ", "minute": "Minute", "minutes": "Minutes", "minutes/consultation": "Minutes/consultation", "missed": "Missed", "missed call": "Missed call", "mission: Wellcare helps you maintain a good health and when you have problems we connect you with the best specialists": "Mission: Wellcare helps you maintain a good health and when you have problems we connect you with the best specialists", "ml, mg, capsule": "Ml, mg, capsule...", "moca e-wallet": "Moca", "momo e-wallet": "<PERSON><PERSON> ", "momo locations": "Momo locations", "momo success": "Success!", "momo success description": "<p>- Wellcare will text you a confirmation including the link to the medical record where you could upload the images and videos.</p><p style=\"margin-bottom:0px\">&nbsp;- Call the doctor: From the link we sent or our mobile app, you have two options: cellular call and internet call.</p>", "money transfer at vnpost or viettel stores": "Money transfer at VNPost or Viettel stores", "month": "Month", "months": "Months", "months old": "months old", "more events": "More events", "my dependent phr": "My Dependent PHR", "my google calendar": "My google calendar", "my information": "My information", "my phr": "My PHR", "my profile": "My profile", "name": "Name", "name require at least 6 characters": "Name require at least 6 characters", "nasal": "nasal", "new phone number": "New phone number", "new update": "New update", "next": "Next", "no": "No", "no appointment today": "No appointment today", "no appointment yet": "No appointment yet", "no consultation yet!": "No consultation yet!", "no data": "No data", "no event yet": "No event yet", "no message yet": "No message yet", "no phone connect": "No phone connect", "no phone yet": "No phone yet", "no questions yet!": "No questions yet!", "no timeslots available, please choose another doctor or call support": "No timeslots available, please choose another doctor or call support", "no treatment note yet": "Patient is awaiting for your note", "not enough money": "Not enough money in wallet", "not fixed": "Not fixed", "not open yet": "Not open yet", "not received otp": "Don't receive the OTP", "not unless you input the proper information": "Not unless you input the proper information", "note": "Note", "note to the delivery man": "Note to the delivery man", "number": "Number", "number days": "{number} days", "number hours": "{number} hours", "number minutes": "{number} minutes", "number of member": "Number of member", "on average": "On average", "once transfered successfully, you will receive SMS from Wellcare, then return to homepage, choose again your timeslot and hit CONFIRM button.": "Once transfered successfully, you will receive SMS from Wellcare, then return to homepage, choose again your timeslot and hit CONFIRM button.", "once you have money in your mHealth account, make an appointment by returning to the home page, selecting a doctor, choosing a time and pressing the CONFIRM button.": "Once you have money in your mHealth account, make an appointment by returning to the home page, selecting a doctor, choosing a time and pressing the CONFIRM button.", "once-a-day": "once a day", "once-a-week": "once a week", "onduty schedule": "Onduty schedule", "online consultation": "Online consultation", "only use this prescription once": "Only use this prescription once", "open": "Open", "open the medical record from SMS or mobile app": "Open the medical record from SMS or mobile app", "open your medical record, press CALL DR button": "Open your link, choose CALL DOCTOR button (There are options to choose cellular or internet call, or set to call from another phone number)", "or": "Or", "or contact": "Or contact", "or top up more for later uses": "Or top up more for later uses", "orally": "Orally", "order confirmation": "Order confirmation", "other": "Others", "other amount": "Other amount", "other files": "Others", "other images": "Other images", "other options": "Other options", "otic": "otic", "otp has been sent": "An OTP has been sent to your registered mobile number, please enter below to verify", "otp has been sent phone please enter below to verify": "An OTP has been sent to <strong>{phone}</strong>, please enter at the box below to verify", "otp resent": "Otp resent", "our doctors responses will not include any diagnoses or medicines": "<span style=\"font-weight:700\">No</span> diagnosis or prescription will be given", "out of office": "Out of office", "out of schedule": "Out of registration date ", "outcome of consultation 70% depends on quality of information you provide": "Outcome of consultation 70% depends on quality of information you provide", "outdated please upgrade new version": "Outdated please upgrade new version", "pack": "Pack", "parent": "Parent", "password": "Password", "password not match": "Password and confirmation password do not match", "password require at least 6 characters": "Password require at least 6 characters", "password reset, login to continue": "Password reset, login to continue", "patient": "Patient", "patient information": "Patient information", "patient is invited to rate doctor and service after consultation completed": "Patient is invited to rate doctor and service after consultation completed", "patient profile": "Patient profile", "patients": "Patients", "patients can select one or multiple appointment slot in an consultation": "Patients can select one or multiple appointment slot in an consultation", "pay": "Pay", "pay directly on momo app": "Through momo e-wallet", "pay directly on zalopay app": "Pay directly on zalopay app", "pay for consultation fee": "Pay for consultation fee", "pay for telemedicine service by credit card, cash at convenient store or bank transfer, eligible for insurance claim": "Pay for telemedicine service by credit card, cash at convenient store or bank transfer. Eligible for insurance claim", "pay for the services conveniently by bank transfer, credit cards, momo e-wallet or cash at nearby convenience stores": "Pay for the services conveniently by bank transfer, credit cards, momo e-wallet or cash at nearby convenience stores", "pay from": "Pay from", "pay via": "Pay via", "payment": "Successful!", "payment code": "Payment code", "payment failed": "Payment failed", "payment method": "Payment methods", "payment successful": "Payment successful", "payment via zalopay wallet": "Payment via ZaloPay wallet", "payoo e-wallet": "<PERSON><PERSON>", "pending": "Comfirmation", "personal information": "Personal information", "personalize features": "Personalize features", "phone": "Call", "phone call": "Phone call", "phone not available": "Phone not available", "phone number": "Phone number", "phone or video call": "Teleconsultation", "switch to teleconsultation": "Switch to teleconsultation", "pill": "<PERSON>ll", "placed": "Already requested delivery", "please agree to the consulting fee policy": "Please agree to the consulting fee policy", "please call": "Please call", "please call ontime": "Please call ontime", "please choose medium before continuing.": "Please choose medium before continuing.", "please choose package before continuing": "Please choose package before continuing.", "please choose private provider before continuing": "Please choose private doctor before continuing", "please choose provider": "Please choose provider", "please choose the time before continuing": "Please choose the time before continuing", "please complete the complete medical record before continuing": "Please complete the complete medical record before continuing", "please confirm": "please confirm", "please confirm question": "Please confirm continuing to \"Proceed\" or change to \"Switch to teleconsultation\"", "please describe in details current health conditions": "Please describe in details current health conditions", "please enter a valid amount": "Please enter a valid amount", "please enter fullname": "Please enter fullname", "please enter main reason for consultation": "Please enter main reason for consultation", "please enter title to continue": "Please enter title to continue", "please enter valid format": "Please enter valid format", "please enter your email": "Please enter your email", "please enter your name": "Please enter your name", "please enter your phone": "Please enter your phone", "please enter your question": "Please enter your question", "please fill in information": "Please fill in information", "please reopen your health record after receiving notification that this consultation is completed": "Please reopen your health record after receiving notification that this consultation is completed", "please select": "Please select", "please select a consultation time frame before continuing": "Please select a consultation time frame before continuing", "please select a person to be examined before continuing": "Please select a person to be examined before continuing", "please select a reason to cancel the consultation": "Please select a reason to cancel the consultation.", "please select another appointment time": "Please select another appointment time.", "please select another password": "Please select another password", "please select duration": "Please select duration before continuing", "please try again": "Please try again", "please upload images and video to your e-medical record (from our mobile app or the link sent through sms) after receiving our confirmation": "Please upload images and video to your e-medical record (from our mobile app or the link sent through sms) <span style=\"    color: #ff5722;\">AFTER</span> receiving our confirmation.", "please wait": "Please wait", "post delivery to home within 3 working days": "COD by postman", "postal code": "Postal code", "pre confirm step question": "Unlike teleconsultation - providing you a two-way communication with a physician, short questions seek to solve only <span style=\"font-weight:700\">simple inquiries</span> and provide no diagnosis or prescription revision. If a specific diagnosis is what you are looking for, then make a", "preferential fee": "<PERSON><PERSON> <PERSON><PERSON> đãi", "premium package": "PREMIUM", "prepaid card": "Prepaid card", "prepaid wellcare card": "Wellcare's Account", "prepare medical record": "Prepare medical record", "prescription": "Prescription", "prescription frequency": "Prescription frequency", "prescription instruction": "Prescription instruction", "prescription not needed for now": "Not needed for now", "prescription take": "Prescription take", "prescriptions": "Prescriptions", "press": "Press", "press 2 to get it or it or text us +*********** (zalo/viber/whatsapp)": "not received OTP? Call +***********, press 2 to get OTP, or contact support team", "press 2 to get it or it or text us {phone}": " Call {phone}, press 2 to get OTP.", "press again to quit app": "Press again to exit the app", "preview": "Preview", "previous consultations": "Previous consultations", "price": "Price", "privacy policy": "Privacy policy", "private": "Private", "private doctor": "Private doctor", "private doctor package": "Private doctor package", "private-doctor": "Private doctor", "processing": "Processing", "promotion code/gift code": "Promotion code/gift code", "province or city": "Province / City", "pt": "Pt", "public": "Public", "quantity": "Quantity", "quantity total": "Total", "question includes only text, no images or video clips.": "Question includes only text, <span style=\"font-weight:700\">no</span> images or video clips.", "questions": "Questions", "quick call": "Quick call", "quickly get to the nearest medical facility in emergency situations": "Quickly get to the nearest medical facilities in emergency situations", "rate your doctor": "Rate your doctor", "rating": "Rating", "re-enter new password": "Re-enter new password", "re-enter password": "Re-enter your password", "recipient email": "Recipient email", "recommended prescription": "RECOMMENDED PRESCRIPTION", "record video": "Record video", "rectal": "rectal", "rectally": "rectally", "register": "Register", "rejected": "Rejected", "relationship": "Relationship", "relationship type": "relationship type", "reload page, or contact": "Reload page, or contact", "remind": "Remind", "remove": "Remove", "remove confirmation": "Remove confirmation", "remove relative": "Remove relative", "remove this event": "Are you sure to remove this event from your calendar", "removed": "Removed", "removed successfully": "Removed successfully", "renewal": "Renewal", "reponse before": "<PERSON><PERSON><PERSON> before", "reputable doctors": "Reputable doctors", "request": "Request", "require headphone and strong, reliable internet": "Stable internet is a must and earphone is preferable", "require strong, reliable Internet from both sides at consultation time": "Require strong, reliable Internet from both sides at consultation time", "required field": "Required field", "required information": "Required", "required question": "Required", "reschedule for an earlier time if available": "Reschedule for an earlier time if available", "rescheduled successfully": "Rescheduled successfully", "reset password": "Reset password", "reset your password": "Reset your Password", "route of administration": "Route of administration", "same meaning": "Same meaning", "satisfied": "Satisfied", "save": "Save", "search": "Search", "search again": "Search again", "search doctor": "Search doctor", "search doctor by name": "Search doctor by name", "search not found": "No results found", "searching": "Searching", "seconds": "Seconds", "secured online medical record, accessible from your computers or phones": "Secured online medical record, accessible from your computers or phones", "security certificates": "Security certificates", "see more": "See more", "see your doctor on time or right after you have abnormal symptoms": "See your doctor on time or right after you have abnormal symptoms", "select a date": "Select a date", "select audio from gallery": "Select audio from gallery", "select call method": "Select call method", "select card type": "Choose card type", "select country code": "Select country code", "select image from gallery": "Select image from gallery", "select video from gallery": "Select video from gallery", "send your first message": "Send your first message", "services": "Services", "set password": "Enter a password", "setting consultation schedule": "Consultation schedule", "setting consultation schedule (private doctor)": "Setting consultation schedule (private doctor)", "setting fee": "Setting fee", "setting for consultations": "Setting for consultations", "share": "Share", "short question": "Short question", "should you have any discomfort while taking medicine, please do get back to the physician for revision of prescription or to the nearest medical center for help.": "Should you have any discomfort while taking medicine, please do get back to the physician for revision of prescription or to the nearest medical center for help.", "show the payment code to the cashier saying you want to pay for the transaction type: 888": "Show the payment code to the cashier saying you want to pay for the transaction type: \"888\".", "shrink": "Shrink", "sibling": "Sibling", "signing up": "Create Personal Health Records", "skip": "<PERSON><PERSON>", "slot duration": "Slot duration", "sms or call +84 366 905 905 to confirm": "Sms or call +84 366 905 905 to confirm.", "sms or phone hotline": "Sms or phone hotline", "sms, phone, or video call anytime to your doctor": "Sms, phone, or video call anytime to your doctor", "soldout": "Soldout", "something went wrong": "Something went wrong.", "sort by": "Sort by", "speciality": "Specialty", "specialty": "Specialty", "spouse": "Spouse", "spray-nasal": "spray nasal", "start": "Start", "start date": "Start date", "started call": "Started call", "stat-in-the-clinic": "STAT in the clinic", "state": "State", "status": "Status", "stay in touch": "Stay in touch", "stay in touch with your doctors whenever you are, easy follow-ups, save your time from traveling": "Stay in touch with your doctors whenever you are. Easy follow-ups. Save your time from traveling.", "step": "Step", "still not satisfied": "Still not satisfied", "store and supermarkets accept momo": "Top up to our momo's account", "stores": "Stores", "stores momo": "Momo store", "stores which support momo topup": "Stores which support momo topup", "strong, reliable internet required": "Strong, reliable internet required", "subcutaneous": "subcutaneous", "sublingual": "sublingual", "submit": "submit", "subtotal": "Subtotal", "subtotal summary": "Subtotal", "success! check your wellcare account (if top-up solely), or sms for the appointment confirmation": "Success! Wellcare will text you a confirmation including the link to the medical record where you could upload the images and videos.\\nCall the doctor: From the link we sent or our mobile app, you have two options: cellular call and internet call.", "successful": "Successful", "successful order confirmation": "Successful order confirmation", "successful payment, via medical link in sms message.": "successful payment, via medical link in sms message.", "successfully added": "Successfully added", "successfully canceled the appointment": "Successfully canceled the appointment!", "suggested prescription": "Recommended prescription", "suggested prescription for reference only - based on information provided by patient on medical record and during consultation, patient should stop using upon adverse symptoms, or visit the nearest clinic": "Recommended prescription is for reference purpose - based on information provided on the medical record and conversation between you and the physician within the context of the teleconsultation. We both understand that you decide whether to take medicine at your own will.", "suitable when you want to keep communicating with doctors until get well": "Suitable when you want to keep communicating with doctors until get well", "suitable when you want to seek help regularly from doctor": "Suitable when you want to seek help regularly from doctor", "support": "Support", "surcharge on Sundays and holidays": "Surcharge on Sundays and holidays", "switch to patient role": "Switch to patient role", "switch to provider role": "Switch to provider role", "synchronize": "Synchronize", "synchronize with Google": "Synchronize with Google", "syringe": "Syringe", "systolic": "systolic", "tablespoon": "Tablespoon", "take picture": "Take picture", "tax code": "Tax code", "tax identification number": "Tax identification number", "teaspoon": "Teaspoon", "technical support": "Technical support", "teleconsultation": "teleconsultation", "telemedicine": "Telemedicine", "telemedicine charge": "Telemedicine charge", "telemedicine expenses may be eligible for insurance claims, please check with your insurers": "Telemedicine expenses may be eligible for insurance claims, please check with your insurers", "telemedicine for adults": "Telemedicine for adults", "telemedicine for kids": "Telemedicine for kids", "telemedicine for mental health": "Telemedicine for mental health", "telemedicine hours": "Telemedicine hours", "telemedicine in three simple steps": "Telemedicine in three simple steps", "telemedicine mobile app": "Telemedicine mobile app", "telemedicine service": "Telemedicine service", "telemedicine: video or phone call with your preferred doctor": "Telemedicine: video or phone call with your preferred doctor", "terms and conditions": "Terms and conditions", "text only, no images or videos": "Text only, no images or videos.", "thanks so much for sharing your experience with us": "Thanks so much for sharing your experience with us", "the appointment time": "The appointment time", "the balance is not enough. please top up or choose another option.": "The balance is not enough. Please top up or choose another option.", "the call will automatically terminate when the allotted consultation time is used up so the next patient appointment can take place": "The call will automatically terminate when the allotted consultation time is used up so the next patient's appointment can take place", "the exact age and gender of the patient is important for doctor answer": "The exact age and gender of the patient is important for doctor answer", "the main reason from 7-10 words": "The main reason from 7-10 words", "the most trusted telemedicine platform": "The most trusted telemedicine platform", "the pride of our doctors": "The pride of our doctors", "the selected date has no available slots, please choose another date": "The selected date has no available slots, please choose another date", "the selected time is invalid": "Time selected is invalid, please select another time", "then text or call": "Then text or call to our hotline", "there will be a notification if the doctor reschedules (sooner or later, because of unexpected events). If it is inconvenient for you, Wellcare may reschedule again": "There will be a notification if the doctor reschedules (sooner or later, because of unexpected events). If it is inconvenient for you, Wellcare may reschedule again.", "there will be a notification if the doctor reschedules sooner or later because of unexpected events if it is inconvenient for you wellcare may reschedule again": "There will be a notification if the doctor reschedules (sooner or later, because of unexpected events). If it is inconvenient for you, contact Wellcare to reschedule again.", "this activation is required only for the first time use.": "This activation is required only for the first time use.", "this consultation": "Consultation", "this field is required, please check to continue": "This field is required, please check to continue", "this field is required, please enter to continue": "This field is required, please enter to continue", "this is medical record link (also in sms)": "This is medical record link (also in sms)", "this month": "This month", "this phone already existed in the system": "This phone already existed in the system", "this timeslot is holding in": "This timeslot is holding in", "this timeslot is on hold for 5 minutes. please move to next step!": "This timeslot is on hold for 5 minutes. Please move to NEXT step!", "this week": "This week", "thousands of thanks": "Thousands of thanks", "three-times-a-day": "three times a day", "throat-spray": "throat spray", "through moca e-wallet": "Through moca e-wallet", "through payoo e-wallet": "Through Payoo e-wallet", "through zalopay e-wallet": "Through zalopay e-wallet", "ticket": "Ticket", "ticket info": "Ticket information", "time": "Time", "time on duty": "On duty", "time patient can call": "Time patient can call", "timezone": "Timezone", "to": "To", "to avoid misadvise, Wellcare may decline to forward those questions that include new symptoms or any topic that has not been discussed with the physician": "To avoid misadvise, Wellcare may decline to forward those questions that include new symptoms or any topic that has not been discussed with the physician.", "to confirm": "to confirm", "to continue": "To continue", "to date": "To date", "to download mobile app": "To download mobile app", "to receive medical record link": "To receive medical record link", "to retry": "To retry", "to system?": "To system?", "to view phone": "To view phone", "today": "Today", "tool": "Tool", "top up via": "Top up via", "top up your prepaid card": "Top up your prepaid card", "topup": "Top up", "topup momo account +84 366 905 905": "Topup momo account +84 366 905 905.", "topup successfully": "Topup successfully", "topup to Momo phone number": "Topup to <PERSON><PERSON> phone number", "topup to continue": "Topup to continue", "total": "Total", "touch the CALL button on time": "Touch the CALL button on time", "touch the call button on the e-medical record ontime": "Touch the call button on the e-medical record ontime", "touch to add your date of birth": "Touch to add your date of birth", "transaction": "Transaction", "transactions and topup": "Transactions and topup", "transfer from a atm card": "Transfer from a ATM card", "transfer from a bank": "Transfer from a bank", "transfer to": "Transfer to", "treatment note": "Notes", "try again": "Try again", "twice-a-day": "twice a day", "type description": "Type description", "type here": "Type here", "type message": "Type message...", "type name to search": "Type name to search...", "type or select": "type or select", "type question": "Type question...", "ultrasound": "Ultrasound", "unable to delete this relative": "Unable to delete this relative", "unit": "Unit", "unlike teleconsultation, Short question solves only simple inquiry about selfcare. Please ask one thing at a time. Keep it short, simple, and straight to the point. No abbreviation or slang.": "Unlike teleconsultation, Short question solves only simple inquiry about selfcare. Please ask one thing at a time. Keep it short, simple, and straight to the point. No abbreviation or slang.", "up to": "Up to", "upcoming appointment": "⏰ Upcoming appointment", "upcoming appointments": "Upcoming appointments", "update": "Update", "update change": "Update change", "update diagnosis": "Update diagnosis", "update failed": "Update failed", "update information": "Update your profile", "update now": "Update now", "updated": "Updated", "updated successfully": "Updated successfully", "upload": "Upload", "upload cancelled": "Upload cancelled", "upload documents": "Upload documents", "upload failed": "Upload failed", "upload file": "Upload", "upload files and videos on next screen": "Upload files and videos on next screen", "upload from gallery": "Upload from gallery", "upload images & video upon completion": "Upload images & video upon <span style=\"color: #ff5722;\">Completion</span>", "upload images & videos": "Upload images & videos", "upload images & videos from the link in the sms or our mobile app assp, doctor will examine them": "Upload images & videos from the link in the sms or our mobile app assp, doctor will examine them", "upload images and videos after you are done with the booking": "Upload images and videos <span style=\"color:#ff5722;\"> AFTER </span> you are done with the booking", "upload images and videos to your e medical record shortly after the appointment is confirmed because the doctor will read it thoroughly cellular call or internet call using wellcare app on schedule": "Upload images and videos to your e-medical record shortly after the appointment is confirmed, because the doctor will read it thoroughly Cellular Call or Internet Call (using Wellcare app) on schedule", "upload images and videos to your emedical record shortly after the appointment is confirmed because the doctor will read it thoroughly video call on schedule through our mobile app you can always switch to cellular once the internet is down": "Upload images and videos to your e-medical record shortly after the appointment is confirmed, because the doctor will read it thoroughly Video Call on schedule through our mobile app (you can always switch to Cellular once the internet is down).", "upload successfully": "Upload successfully", "urethral": "urethral", "usable": "Usable", "usable call times": "Usable call times", "use another account": "Use another account", "use another phone": "Use another phone", "use later": "Use later", "use within": "Use within", "username is not valid": "Username is not valid", "vaccine": "Vaccine", "vaginal": "vaginal", "vat invoice": "VAT invoice", "verify": "Verify", "verify and create account": "Verify and Create an account", "video": "Video", "video call": "Video call", "video call fee": "Video call fee", "view": "View", "view and edit consultation": "View and edit consultation", "view detail": "View detail", "view image": "View image", "view medical record": "View medical record", "visa/mastercard/jcb card": "Through Visa/Mastercard/JCB card", "vision: the most TRUSTED telemedicine platform": "Vision: the most TRUSTED telemedicine platform", "vnpost or viettel stores": "Money transfer at VNPost or Viettel Stores", "voice call by internet": "Voice call by Internet", "voice call fee": "Voice call fee", "wait a second": "Wait a second", "wards": "Wards", "we help you maintain good health and when you have problems, we connect you to the best specialists via phone or video 24/7": "We help you maintain good health and when you have problems, we connect you to the best specialists via phone or video 24/7", "we may cancel the appointment unless you provide all the necessary medical information as requested": "We may refuse the appointment unless you provide all the necessary medical information as requested.", "we understand that this registered number belongs to you - an adult (above 16 years old). If the patient is someone else, please provide it in the later section.": "We understand that this registered number belongs to you - an adult (above 16 years old). If the patient is someone else, please provide it in the later section.", "we will check if you are a new or an old friend and prompt you to login or register new account": "We will check if you are a new or an old friend and prompt you to login or register new account", "we will contact you as soon as possible": "We will contact you as soon as possible", "week": "Week", "weekend": "Weekend fee", "weeks": "Weeks", "weeks old": "weeks old", "weight": "Weight", "wellcare mobile app": "Wellcare mobile app", "wellcare offers free consultation with another specialist, even with higher fee": "Wellcare offers free consultation with another specialist, even with higher fee", "wellcare prepaid card": "Wellcare prepaid card", "wellcare telemedicine": "Wellcare telemedicine", "wellcare's choice": "Wellcare's choice", "wellness wallet": "Wellness wallet", "what patients say?": "What patients say?", "who is the patient?": "Who is the patient?", "whom is this consultation for?": "Whom is this consultation for?", "with me": "With me", "within": "Within", "within 30m after payment received, Wellcare will sms to your registered phone a confirmation together with medical record link on which you can update record and call doctor": "<p>Make the payment transfer from your bank. Input your mobile phone number as the note of bank transfer content.</p>\n<p>Within 30 minutes after receiving the payment, Wellcare will send you a text message to confirm, including the link to the medical record where you could upload the images and videos.</p>\n<p style=\"margin-bottom:0px\">Call the doctor: From the link we sent or our mobile app, you have two options: cellular call and internet call.</p>", "work location": "Work location", "work schedule": "Work schedule", "workplace": "Workplace", "xray": "XRay", "years": "Years", "years old": "Y.O. ", "yes": "yes", "you": "You", "you agree with": "You agree with", "you are choosing to send our physians an inquiry before proceeding with a short question please note that question includes only text no images or video clips no diagnosis or prescription will be given doctors wellcare reserve the right to decline if the inquiry is having less information than what is needed the patient should make a voice or video call with our doctor for a proper diagnosis or come to the nearest medical center soon": "<p>You are choosing to send our physians an inquiry. Before proceeding with a short question, please note that:<br>- Question includes only text, <strong>no</strong> images or video clips.<br>- <strong>No</strong> diagnosis or prescription will be given<br>- Doctors & Wellcare reserve the right to decline, if:<br>  1. The inquiry is having less information than what is needed.<br>  2. The patient should make a voice or video call with our doctor for a proper diagnosis or come to the nearest medical center soon.<br>.<br><em>Clarification: Unlike teleconsultation - providing you a two-way communication with a physician, short questions seek to solve only </em><strong><em>simple inquiries</em></strong><em> and provide no diagnosis or prescription revision. If a specific diagnosis is what you are looking for, then make a teleconsultation to receive a medical form to fill with more information, relevant images & videos, and to have our physicians interpret all of your symptoms to you.</em></p>", "you are missing some required information. Please follow the steps again.": "You are missing some required information. Please follow the steps again.", "you are offline": "You are offline", "you can move the appointment time sooner or later. Don't forget patient calls only within the appointment slot, but you may call any time.": "You can reschedule to the earlier or later time, and the patient will accordingly call within the new allotted timeslot. But remember: unlike the patient, you can call anytime, at your convenience.", "you have entered the promo code": "You have entered the promo code", "you have read the consultation fee policy, agreed to deduct the percentage of cancellation fee and refund the remaining amount to the examination book for the next consultation": "You have read the consultation fee policy, agreed to deduct the percentage of cancellation fee and refund the remaining amount to the examination book for the next consultation.", "you might get the answer before": "You might get the answer before", "you need additional topup": "You need to top up at least {amount} for this consultation", "you need to top up": "You need to top up", "you will be able to upload images and videos to your e-medical record from our mobile app or the link we sent through sms after you are done with the booking": "You will be able to upload images and videos to your e-medical record from our mobile app or the link we sent through sms <span style=\"color: #ff5722;\">AFTER</span> you are done with the booking", "you will be in regular contact with your doctor through voice and video calls.": "You will be in regular contact with your doctor through voice and video calls.", "you will receive the response within 24 hours": "You will receive the response within 24 hours. Please check your SMS or Wellcare mobile app", "your account has been created successfully": "Your account has been created successfully", "your appointment time is": "Your appointment time is", "your avatar": "Your avatar", "your balance": "Your balance", "your current session is expired": "Your current session is expired, please login again to continue", "your data is secured with HIPPA compliance": "Your data is secured with HIPPA compliance", "your feedback about the system and its people": "Your feedback about the system and its people", "your full name": "Your full name", "your name": "Your name", "your phone number is the login id and is use to connect with wellcare": "Your phone number is the login id and is use to connect with Wellcare", "your question?": "Your question?", "your rating reflects the doctors credibility and his her raking on our system": "Your rating reflects the doctor's credibility and his/her raking on our system", "your relatives": "Your relatives", "your thought about the doctor your thought about wellcare": "- Your thought about the doctor\\n- Your thought about the system", "zalopay": "Payment ZaloPay", "zalopay e-wallet": "ZaloPay", "please fill out the information below and include your avatar": "Please complete the information below and include your avatar", "you need to add an avatar to continue": "You need to add an avatar to continue", "payment:order:success:download:title": "Download Our App to Connect Doctor", "payment:order:success:download:description": "Download our app to call, receive prescriptions, read the notes and access important health information", "payment:order:success:download:support": "Technical support staff", "Service Registration - Wellcare": "Service Registration - Wellcare", "Service Payment - Wellcare": "Service Payment - Wellcare"}