<template>
  <v-app>
    <v-app-bar
      v-if="isWebViewApp && $config.appName"
      app
      dense
      color="primary"
      fixed
      clipped-right
      class="white--text"
    >
      <v-btn color="secondary" icon @click="onBack">
        <v-icon>$left</v-icon>
      </v-btn>
      <span
        class="text-uppercase font-weight-bold w-app-title white--text"
        style="line-height: initial; width: 80%; text-align: center"
      >
        {{ $t(title) }}
      </span>
    </v-app-bar>
    <v-main>
      <nuxt />
    </v-main>
    <Notification color="primary" text />
    <!-- <zalo-chatbot /> -->
  </v-app>
</template>

<script lang="ts">
import { Vue, Component } from 'nuxt-property-decorator'
import ZaloChatbot from '@/components/zalo-chatbot.vue'
@Component({
  head() {
    return {
      title: `${
        this.$config.appName?.toUpperCase() ?? 'Đăng ký dịch vụ'
      } - Wellcare`
    }
  },
  middleware: ['authenRedirect', 'track', 'checkMembership'],
  components: {
    ZaloChatbot
  }
})
export default class Layout extends Vue {
  isWebView: any

  onBack() {
    this.$router.back()
  }

  get title() {
    return this.$store.state.title
  }

  get isWebViewApp() {
    if (this.$config.appName) {
      return true
    } else {
      return this.isWebView
    }
  }

  mounted() {
    this.$store.commit('SET_TITLE', 'confirmation')
    this.$gtm?.push({
      event: 'ipEvent',
      userId: this.$store.state?.authen?.user?._id
    })
    fetch('https://api.ipify.org?format=json')
      .then((x) => x.json())
      .then(({ ip }) => {
        console.log(19, 'https://api.ipify.org?format=json', ip)
        this.$store.commit('setClientIp', ip)
        this.$gtm?.push({
          event: 'ipEvent',
          ipAddress: ip,
          userId: this.$store.state?.authen?.user?._id
        })
      })
  }
}
</script>
<style scoped>
.w-app-title {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
