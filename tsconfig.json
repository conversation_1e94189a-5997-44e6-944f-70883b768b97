{
  "compilerOptions": {
    "target": "ES2018",
    "module": "ESNext",
    "moduleResolution": "Node",
    "lib": [
      "ESNext",
      "ESNext.AsyncIterable",
      "DOM"
    ],
    "esModuleInterop": true,
    "experimentalDecorators": true,
    "sourceMap": true,
    "noImplicitAny": false,
    "noEmit": true,
    "resolveJsonModule": true,
    "baseUrl": ".",
    "paths": {
      "~/*": [
        "./src/*"
      ],
      "@/*": [
        "./src/*"
      ],
      "@type": [
        "index",
        "shims-vue",
        "shims-tsx"
      ]
    },
    "types": [
      "jest",
      "@types/node",
      "@nuxt/vue-app",
      "@nuxt/types",
      "@nuxtjs/vuetify",
      "@nuxtjs/i18n",
      "@types/webpack-env",
      "@nuxtjs/moment",
      "@nuxtjs/axios",
      "@nuxtjs/gtm",
      "@wellcare/vue-authen",
      "@nuxtjs/device",
      "@nuxtjs/sentry",
      "./index",
      "./shims-vue",
      "./shims-tsx"
    ],
  },
  "exclude": [
    "node_modules", ".nuxt", "dist"
  ]
}
