{"name": "@wellcare/checkout-wellcare", "version": "1.0.0", "description": "Checkout", "author": "mHealth Technologies JSC", "license": "Private", "private": true, "scripts": {"dev": "nuxt -o --config-file nuxt.config.ts", "build": "nuxt build --config-file nuxt.config.ts", "start": "nuxt start --config-file nuxt.config.ts", "export": "nuxt export", "serve": "nuxt serve", "lint": "eslint --ext .ts,.js,.vue src/ --fix", "test": "jest"}, "dependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@mdi/js": "^6.6.96", "@nuxtjs/axios": "^5.13.1", "@nuxtjs/composition-api": "^0.32.0", "@nuxtjs/dayjs": "^1.4.1", "@nuxtjs/firebase": "^8.2.2", "@nuxtjs/google-fonts": "^2.0.0", "@nuxtjs/gtm": "^2.4.0", "@nuxtjs/i18n": "^7.2.0", "@nuxtjs/moment": "^1.6.1", "@nuxtjs/robots": "^2.4.2", "@nuxtjs/toast": "^3.3.1", "@tanstack/vue-query": "4.27.0", "@vueuse/nuxt": "9.1.0", "@wellcare/nuxt-module-account": "1.2.6", "@wellcare/nuxt-module-chat": "0.0.55", "@wellcare/nuxt-module-checkout": "1.2.9", "@wellcare/nuxt-module-content": "0.1.1", "@wellcare/nuxt-module-data-layer": "0.10.12", "@wellcare/nuxt-module-elastic": "0.4.1", "@wellcare/nuxt-module-feeds": "0.1.5", "@wellcare/nuxt-module-media": "0.5.0", "@wellcare/nuxt-module-phr": "0.1.68", "@wellcare/vue-authen": "1.1.11", "@wellcare/vue-component": "1.0.10", "@wellcare/web-client": "1.3.25", "async": "^3.2.0", "base64url": "^3.0.1", "cookie-universal-nuxt": "^2.1.4", "eslint-webpack-plugin": "^2.5.2", "firebase": "^9.9.0", "jsonwebtoken": "^8.5.1", "lazysizes": "^5.3.2", "lg-rotate.js": "^1.2.0", "lg-thumbnail.js": "^1.2.0", "lg-video.js": "^1.3.0", "lg-zoom.js": "^1.3.0", "lightgallery.js": "^1.4.0", "nuxt": "2.15.2", "nuxt-blurhash": "^0.0.6", "nuxt-logger": "^0.2.0", "nuxt-property-decorator": "^2.9.1", "nuxt-user-agent": "^1.2.2", "nuxtjs-microsoft-clarity": "^1.1.5", "vue-debounce-decorator": "^1.0.1", "vuetify-loader": "1.4.3", "vuex-map-fields": "^1.4.1"}, "devDependencies": {"@nuxt/types": "^2.15.3", "@nuxt/typescript-build": "^2.1.0", "@nuxtjs/eslint-config": "^6.0.0", "@nuxtjs/eslint-config-typescript": "^6.0.0", "@nuxtjs/eslint-module": "^3.0.2", "@nuxtjs/vuetify": "^1.12.3", "@types/webpack-env": "^1.16.0", "@vue/test-utils": "^1.1.2", "babel-eslint": "^10.1.0", "babel-jest": "^26.6.3", "core-js": "3", "deepmerge": "^4.2.2", "eslint": "^7.21.0", "eslint-config-prettier": "^8.1.0", "eslint-loader": "^4.0.2", "eslint-plugin-nuxt": "^2.0.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^7.7.0", "eslint-plugin-vuetify": "^1.0.0-beta.8", "jest": "^26.6.3", "nuxt-typed-vuex": "^0.1.22", "nuxt-webpack-optimisations": "^2.2.0", "prettier": "^2.2.1", "sass": "~1.32", "sass-loader": "^10.1.1", "ts-jest": "^26.5.3", "ts-loader": "^8.0.17", "typescript": "^4.1.5", "vue-browser-detect-plugin": "^0.1.13", "vue-jest": "^3.0.7", "webpack": "4.46.0", "canvas-confetti": "^1.9.3"}, "resolutions": {"vue": "2.6.14", "vue-server-renderer": "2.6.14", "vue-template-compiler": "2.6.14", "@vueuse/core": "10.1.2", "@nuxt/kit": "3.6.0", "glob": "7.2.3"}}