pipeline {
    agent any
    environment {
        registry = 'mhealthvn/vuetify'
        registryCredential = 'mhealthvn'
        GIT_TOKEN = credentials('git-token')
    }
    stages{
        stage('Build Image') {
            steps {
                sh "docker build --rm -t $registry:$GIT_BRANCH . --build-arg GIT_COMMIT=$GIT_COMMIT --build-arg GIT_BRANCH=$GIT_BRANCH --build-arg GIT_TOKEN=$GIT_TOKEN"
            }
        }        
        stage('Deploy Image') {
            steps{
                script {
                    docker.withRegistry( '', registryCredential ) {
                        sh "docker push $registry:${GIT_BRANCH}"                    
                    }
                }
            }
        }
    }
}