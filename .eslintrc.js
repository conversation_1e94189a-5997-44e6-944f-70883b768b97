module.exports = {
  root: true,
  env: {
    es6: true,
    browser: true,
    node: true,
  },
  parserOptions: {
    parser: "@typescript-eslint/parser",
    ecmaFeatures: {
      legacyDecorators: true,
    },
  },
  extends: [
    "@nuxtjs/eslint-config-typescript",
    "plugin:nuxt/recommended",
    "plugin:prettier/recommended",
    "plugin:vue/strongly-recommended",
    "plugin:import/typescript",
    "prettier",
  ],
  plugins: ["prettier", "standard", "vue", "vuetify"],
  rules: {
    "array-callback-return": "off",
    "no-eval": 0,
    "no-undef": 0,
    "no-console": 0,
    "no-implied-eval": 2,
    "prettier/prettier": 0,
    "vue/no-v-html": 0,
    "no-new": 0,
    "max-lines": [
      "error",
      { max: 1000, skipComments: true, skipBlankLines: true },
    ],
    "vue/name-property-casing": ["error", "kebab-case"],
    "no-unused-vars": "off",
    "@typescript-eslint/no-unused-vars": "off",
    "lines-between-class-members": [
      "off",
      "always",
      { exceptAfterSingleLine: true },
    ],
    camelcase: [2, { properties: "never" }],
    "no-unneeded-ternary": [0, { defaultAssignment: false }],
    "vuetify/grid-unknown-attributes": "warn",
    "vuetify/no-legacy-grid": 1,
    "import/order": [
      "off",
      {
        groups: [
          "index",
          "sibling",
          "parent",
          "internal",
          "external",
          "builtin",
        ],
      },
    ],
    "vue/attribute-hyphenation": [
      "error",
      "always",
      {
        ignore: ["editorToolbar"],
      },
    ],
    "vue/html-self-closing": [
      "error",
      {
        html: { normal: "never", void: "always" },
        svg: "always",
        math: "always",
      },
    ],
  },
};
