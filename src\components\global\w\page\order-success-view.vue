<template>
  <div>
    <v-card
      :width="$vuetify.breakpoint.xsOnly ? '90%' : '500'"
      class="mt-10 py-10 mx-auto"
      elevation="0"
      color="transparent"
    >
      <v-dialog-bottom-transition v-if="loading">
        <w-loading :value="true" />
      </v-dialog-bottom-transition>
      <div v-else class="d-flex align-center justify-center flex-column">
        <span>
          <img src="/order-success.svg" />
        </span>

        <div class="text-20 font-weight-bold mt-3 mb-10">
          {{ $t('order successful') }}
        </div>
        <div class="d-flex">
          <!-- <v-btn
            class="rounded-lg tracking-normal mx-2"
            large
            depressed
            style="border: solid 1px rgba(204 204 204 / 50%)"
            @click="directToHome({ isReload: true })"
          >
            {{ $t('back to homepage') }}
          </v-btn> -->

          <v-btn
            class="rounded-lg tracking-normal mx-1"
            large
            color="primary"
            depressed
            @click="directToDownloadApp()"
          >
            {{ $t('download app') }}
          </v-btn>
        </div>
      </div>
    </v-card>
  </div>
</template>
<script lang="ts">
import {
  computed,
  defineComponent,
  ref,
  useContext,
  useRoute,
  useRouter,
  useStore
} from '@nuxtjs/composition-api'
import { useAuth, useUser } from '@wellcare/nuxt-module-account/repositories'
export default defineComponent({
  props: {
    consultationId: {
      type: String,
      default: '',
      required: true
    },
    isShortQuestion: {
      type: Boolean,
      default: false,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const { user } = useAuth()
    const router = useRouter()
    const route = useRoute()
    const { state, commit } = useStore()
    const { renewStoreAuthen } = useUser()

    const DOWNLOAD_LINK = {
      ios: 'https://apps.apple.com/us/app/wellcare/id1039423586?ls=1',
      android: 'https://play.google.com/store/apps/details?id=vn.wellcare'
    }
    const { app } = useContext()
    const isIOS = computed(() => app?.$ua?.isFromIos())
    const isFromMobile = computed(
      () => app?.$ua?.isFromMobilephone() || app?.$ua?.isFromSmartphone()
    )

    const isPregnancyDiary = computed<boolean>(
      () => (state as any)?.checkout?.pregnancy?.isPregnancy
    )

    const products = computed(() =>
      JSON.parse((route.value?.query?.product as string) ?? '[]')
    )
    const isMembershipProduct = computed(() => {
      try {
        return products.value.includes('membership')
      } catch {
        return false
      }
    })
    const directTo = ({
      path,
      isReload = false
    }: {
      path: string
      isReload?: boolean
    }) => {
      if (isReload) {
        window.location.href = path
      } else {
        router.push(path)
      }
    }

    const directToHome = ({ isReload = false }: { isReload?: boolean }) => {
      const pathToHome = '/'
      directTo({ path: pathToHome, isReload })
    }

    const directToConsultation = () => {
      directTo({ path: `/consultations/${props.consultationId}` })
    }

    const directToDownloadApp = () => {
      window.location.href = 'https://khamtuxa.vn/download'
    }

    const directToDiary = async () => {
      await renewStoreAuthen()
      directTo({
        path: '/membership/health-programs/pregnancy-diary',
        isReload: false
      })
      commit('checkout/SET_PREGNANCY', false)
    }

    const downloadModal = ref(false)

    const onInstallButtonClick = () => {
      if (isFromMobile.value && process.client) {
        const downloadLink = isIOS.value
          ? DOWNLOAD_LINK.ios
          : DOWNLOAD_LINK.android
        window.open(downloadLink, '_blank')
      } else downloadModal.value = true
    }

    return {
      products,
      isFromMobile,
      directToHome,
      downloadModal,
      directToDiary,
      isPregnancyDiary,
      isMembershipProduct,
      directToDownloadApp,
      onInstallButtonClick,
      directToConsultation
    }
  }
})
</script>
