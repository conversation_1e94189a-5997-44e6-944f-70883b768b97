<template>
  <div
    class="zalo-chat-widget"
    data-oaid="2727084330920973261"
    data-welcome-message="Rất vui khi được hỗ trợ bạn!"
    data-autopopup="0"
    data-width=""
    data-height=""
    style=""
  ></div>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator'
@Component
export default class ZaloChatbot extends Vue {
  mounted() {
    const zaloChatbot = document.createElement('script')
    zaloChatbot.setAttribute('src', 'https://sp.zalo.me/plugins/sdk.js')
    document.head.appendChild(zaloChatbot)
  }
}
</script>
<style scoped>
.zalo-chat-widget {
  right: 8px;
  z-index: 0;
  border: none;
  visibility: visible;
  position: fixed;
  width: 60px;
  height: 60px;
  bottom: 76px !important;
  left: unset !important;
}

@media only screen and (max-width: 600px) {
  .zalo-chat-widget {
    bottom: 96px !important;
  }
}
</style>
