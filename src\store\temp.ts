import { GetterTree, MutationTree } from 'vuex'
import { getField, updateField } from 'vuex-map-fields';
import { RootState } from '.'

export const strict = false

export const namespaced = true

export const state = () => ({
    title: '',
    amountOfDoctor: 0
})

export type PaymentModuleState = ReturnType<typeof state>

export const getters: GetterTree<PaymentModuleState, RootState> = {
    getField
}

export const mutations: MutationTree<PaymentModuleState> = {
    updateField,
    SET_TITLE(state, title) {
    state.title = title;
    },
    SET_AMOUNTOFDOCTOR(state, amountOfDoctor) {
        state.amountOfDoctor = amountOfDoctor;
    },
}