export const checkoutConfig = (env) => ({
  prefix: 'w',
  level: 4,
  vat: {
    show: false,
    useOrganizationId: '1234'
  },
  fee: {
    hidden: true
  },
  beneficiary: {
    meOnly: true
  },
  payment: {
    showFee: true,
    consultationLink: env.PAYMENT_CONSULTATION_LINK,
    gatewayId: env.PAYMENT_GATEWAY_ID,
    acceptWalletTypes: ['cash', 'membership'],
    promotion: env.PAYMENT_PROMOTION_SLUG,
    wallet: {
      cash: {
        title: 'wellcare-prepaid',
        label: 'wellcare card',
        className: 'service-card',
        titleColor: {
          color: 'rgb(130, 255, 243)',
          textShadow: '0px 4px 4px #2a7d75'
        },
        colorAccount: {
          color: '#194F48',
          textShadow: '0px 4px 4px rgba(132, 182, 161, 0.26)'
        }
        // color: ['#01463f', '#009688'],
        // icon: '$wallet-outline',
        // wellcareLogo: true,
      },
      membership: {
        title: 'premiere',
        label: 'membership card',
        className: 'member-card',
        titleColor: {
          color: '#ffe1d7',
          textShadow: '0px 4px 4px #903a1d'
        },
        colorAccount: {
          color: '#8C2502',
          textShadow: '0px 4px 4px rgba(132, 182, 161, 0.26)'
        }
        // color: ['#243b55', '#141e30'],
        // image: `/icons/diamond.png`,
        // wellcareLogo: true,
      }
    },
    membership: {},
    sub: {
      match: [/manulife/]
    },
    addOnServices: true,
    voucher: {
      allow: true,
      cancel: true,
      onlyVoucher: false,
      autoApply: false,
      policy: null,
      code: null
    }
  },
  topup: {
    sources: env.TOPUP_SOURCES,
    zalopay: {
      baseUrl: env.ZALOPAY_URL,
      appId: env.ZALOPAY_APP_ID
    },
    momo: {},
    bank: {
      accounts: [
        {
          number: '*************',
          beneficiary: 'CTCP CONG NGHE MHEALTH -',
          bankName: '- NH Vietcombank (CN TP HCM)'
        }
      ]
    }
  },
  forWellcare: true,
  UIcustomize: {
    chooseMedium: {
      showFee: true
    },
    chooseProviderTimeslot: {
      showFee: true,
      showAlert: true
    },
    question: {
      placeholder:
        'unlike teleconsultation, Short question solves only simple inquiry about selfcare. Please ask one thing at a time. Keep it short, simple, and straight to the point. No abbreviation or slang.'
      // 'unlike teleconsultation, Short question solves only simple inquiry about selfcare. Please ask one thing at a time. Keep it short, simple, and straight to the point. No abbreviation or slang.'
    },
    choosePatient: {
      canAddRelationship: true
    },
    prepareMedicalRecord: {
      showLabel: true
    },
    stepper: {
      showArrowIcon: false
    }
  }
})
