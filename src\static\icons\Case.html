<!doctype html>
<html lang="vi" data-n-head="%7B%22lang%22:%7B%221%22:%22vi%22%7D%7D">
  <head >
    <title>Template Nuxt Vuetify</title><meta data-n-head="1" charset="utf-8"><meta data-n-head="1" name="title" content="video conferencing"><meta data-n-head="1" name="description" content="video conferencing"><meta data-n-head="1" name="viewport" content="width=device-width, initial-scale=1.0"><meta data-n-head="1" name="robots" content="noindex,noarchive,nofollow"><link data-n-head="1" rel="icon" type="image/x-icon" href="https://mhealth-beta.s3.amazonaws.com/cms/11CuLAAmuGVP2nO3.png"><link data-n-head="1" rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900"><link data-n-head="1" rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900|Material+Icons"><link data-n-head="1" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/3.7.2/animate.min.css" rel="stylesheet"><link data-n-head="1" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.52.0/codemirror.min.css" rel="stylesheet"><link data-n-head="1" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.52.0/theme/material.min.css" rel="stylesheet"><link data-n-head="1" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.52.0/addon/scroll/simplescrollbars.min.css" rel="stylesheet"><link data-n-head="1" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.52.0/addon/hint/show-hint.min.css" rel="stylesheet"><link data-n-head="1" href="/redactor/css/redactor.min.css" rel="stylesheet"><link data-n-head="1" href="/redactor/css/inlinestyle.min.css" rel="stylesheet"><link data-n-head="1" href="/redactor/css/filemanager.min.css" rel="stylesheet"><script data-n-head="1" src="/redactor/js/redactor3.js" type="module" required></script><script data-n-head="1" src="/redactor/js/alignment.min.js" type="module"></script><script data-n-head="1" src="/redactor/js/definedlinks.min.js" type="module"></script><script data-n-head="1" src="/redactor/js/fontfamily.min.js" type="module"></script><script data-n-head="1" src="/redactor/js/fontsize.min.js" type="module"></script><script data-n-head="1" src="/redactor/js/fullscreen.min.js" type="module"></script><script data-n-head="1" src="/redactor/js/table.min.js" type="module"></script><script data-n-head="1" src="/redactor/js/video.min.js" type="module"></script><script data-n-head="1" src="/redactor/js/imagemanager.min.js" type="module"></script><script data-n-head="1" src="/redactor/js/counter.min.js" type="module"></script><base href="/"><link rel="preload" href="/_nuxt/a440cda.js" as="script"><link rel="preload" href="/_nuxt/bf73228.js" as="script"><link rel="preload" href="/_nuxt/e8f6fb6.js" as="script"><link rel="preload" href="/_nuxt/4e9f35f.js" as="script">
  </head>
  <body >
    <div id="__nuxt"><style>#nuxt-loading {  background: white;  visibility: hidden;  opacity: 0;  position: absolute;  left: 0;  right: 0;  top: 0;  bottom: 0;  display: flex;  justify-content: center;  align-items: center;  flex-direction: column;  animation: nuxtLoadingIn 10s ease;  -webkit-animation: nuxtLoadingIn 10s ease;  animation-fill-mode: forwards;  overflow: hidden;}@keyframes nuxtLoadingIn {  0% {visibility: hidden;opacity: 0;  }  20% {visibility: visible;opacity: 0;  }  100% {visibility: visible;opacity: 1;  }}@-webkit-keyframes nuxtLoadingIn {  0% {visibility: hidden;opacity: 0;  }  20% {visibility: visible;opacity: 0;  }  100% {visibility: visible;opacity: 1;  }}#nuxt-loading>div,#nuxt-loading>div:after {  border-radius: 50%;  width: 5rem;  height: 5rem;}#nuxt-loading>div {  font-size: 10px;  position: relative;  text-indent: -9999em;  border: .5rem solid #F5F5F5;  border-left: .5rem solid #D3D3D3;  -webkit-transform: translateZ(0);  -ms-transform: translateZ(0);  transform: translateZ(0);  -webkit-animation: nuxtLoading 1.1s infinite linear;  animation: nuxtLoading 1.1s infinite linear;}#nuxt-loading.error>div {  border-left: .5rem solid #ff4500;  animation-duration: 5s;}@-webkit-keyframes nuxtLoading {  0% {-webkit-transform: rotate(0deg);transform: rotate(0deg);  }  100% {-webkit-transform: rotate(360deg);transform: rotate(360deg);  }}@keyframes nuxtLoading {  0% {-webkit-transform: rotate(0deg);transform: rotate(0deg);  }  100% {-webkit-transform: rotate(360deg);transform: rotate(360deg);  }}</style><script>window.addEventListener('error', function () {  var e = document.getElementById('nuxt-loading');  if (e) {e.className += ' error';  }});</script><div id="nuxt-loading" aria-live="polite" role="status"><div>Loading...</div></div></div><script data-n-head="1" src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.52.0/codemirror.min.js" data-body="true" type="text/javascript"></script><script data-n-head="1" src="https://cdn.jsdelivr.net/npm/vue-codemirror@4.0.6/dist/vue-codemirror.min.js" data-body="true" type="text/javascript"></script><script data-n-head="1" src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.52.0/mode/javascript/javascript.min.js" data-body="true" type="text/javascript"></script><script data-n-head="1" src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.52.0/addon/display/autorefresh.min.js" data-body="true" type="text/javascript"></script><script data-n-head="1" src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.52.0/addon/selection/active-line.min.js" data-body="true" type="text/javascript"></script><script data-n-head="1" src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.52.0/addon/scroll/simplescrollbars.min.js" data-body="true" type="text/javascript"></script><script data-n-head="1" src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.52.0/addon/hint/show-hint.min.js" data-body="true" type="text/javascript"></script><script data-n-head="1" src="/redactor/js/fontcolor.min.js" type="module" data-body="true"></script><script data-n-head="1" src="https://unpkg.com/xlsx/dist/shim.min.js" data-body="true"></script><script data-n-head="1" src="https://unpkg.com/xlsx/dist/xlsx.full.min.js" data-body="true"></script><script>window.__NUXT__={config:{cdnHost:"https:\u002F\u002Fd9iixa2xxa0x2.cloudfront.net\u002Ffile\u002F",accountBaseUrl:"https:\u002F\u002Faccount.wellcare.vn",apiGatewayEndpoint:"https:\u002F\u002Fapis.wellcare.vn\u002Fgateway",endpoint:"https:\u002F\u002Fapis.wellcare.vn",socketEndpoint:"https:\u002F\u002Fsocketio.wellcare.vn",jwt:{appId:"5c4224f6ee6065f67eaedb46",appSecret:"6d321a86-6ad4-43f1-a4ac-826d40be805b"}},staticAssetsBase:void 0}</script>
  <script src="/_nuxt/a440cda.js"></script><script src="/_nuxt/bf73228.js"></script><script src="/_nuxt/e8f6fb6.js"></script><script src="/_nuxt/4e9f35f.js"></script></body>
</html>
