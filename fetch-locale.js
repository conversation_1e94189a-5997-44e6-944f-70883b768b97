const fs = require('fs')
const mkdirp = require('mkdirp')
const rimraf = require('rimraf')
const async = require('async')
const mhealth_api = require('@wellcare/mhealth_api')
const nodeEnv = require('dotenv').config().parsed
const APP_NAME = 'checkout'
const env = {
  endpoint: 'https://apis.wellcare.vn',
  accountBaseUrl: nodeEnv.ACCOUNT_BASE_URL
}

var token = Buffer.from(
  `5d2ee7f2db8376052b982da9:90b3f1a8-68a6-44fd-830d-5e16011c2310`,
  'utf-8'
).toString('base64')
/********************
 * INIT MHEALTH_API *
 ********************/
var api = new mhealth_api({
  appId: '5d2ee7f2db8376052b982da9',
  appSecret: '90b3f1a8-68a6-44fd-830d-5e16011c2310',
  token: token,
  env
})

/********************
 * REMOVE OLD FILES *
 ********************/
let dirPath = `./src/locales`
rimraf(dirPath, function () {})

/**************
 * FETCH ENTITY *
 **************/
async.waterfall(
  [
    // step 1: fetch all entities required for the app
    function fetchTranslation(callback) {
      api
        .init()
        .list('translation')
        .search({
          type: 'app',
          app: {
            $in: [
              `${APP_NAME}`,
              'all',
              'checkout',
              'webview',
              'account',
              'payment'
            ]
          }
        })
        .count()
        .sort('key')
        .limit(999999)
        .fields('key translation')
        .exec(callback)
    },
    // step 2: prepare entity attributes
    function saveToFiles(res, callback) {
      let records = res.results
      let locales = ['vi', 'fr', 'en']
      async.each(
        locales,
        function (locale, cb) {
          let translation = {}
          records.forEach((i) => {
            translation[i.key] = i.translation[locale]
          })
          let filePath = `${dirPath}/${locale}.json`
          mkdirp(dirPath, function (err) {
            fs.writeFile(
              filePath,
              JSON.stringify(translation, null, 2),
              (err) => {
                if (err) console.error(err)
              }
            )
          })
          console.log(
            '✅ ',
            locale,
            ' ',
            Object.keys(translation).length,
            ' translation'
          )
        },
        callback
      )
    }
  ],
  function (err, data) {
    if (err) {
      console.error('❌ ', err.error)
      if (err.body && err.body.response) {
        console.log('___status: ', err.body.response.statusText)
        if (err.body.response.data) {
          console.log('___message:', err.body.response.data.status)
        }
      }
      process.exit(1)
    }
  }
)
