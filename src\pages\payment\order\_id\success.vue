<template>
  <w-order-membership-success v-if="hasMembershipProduct" />
  <v-container v-else fluid class="pa-0" style="min-height: 100vh">
    <v-row align="center" style="min-height: 90vh" class="ma-0">
      <v-col cols="12" md="7">
        <div class="max-width-container">
          <h1 class="text-h2 font-weight-bold mb-4">
            {{ $t('payment:order:success:download:title') }}
          </h1>
          <p class="text-h5 mb-12">
            {{ $t('payment:order:success:download:description') }}.
          </p>

          <div class="store-buttons d-flex flex-column flex-sm-row gap-4">
            <!-- Google Play Store button -->
            <a
              href="https://play.google.com/store/apps/details?id=vn.wellcare"
              class="hover-scale mb-4 mb-sm-0 store-button"
            >
              <img
                :src="`/images/download/${i18n.locale}-google-play-badge.png`"
                alt="Get it on Google Play"
                width="208"
                style="height: 97%"
              />
            </a>

            <!-- App Store button -->
            <a
              href="https://apps.apple.com/us/app/wellcare/id1039423586"
              class="hover-scale store-button"
            >
              <img
                :src="`/images/download/${i18n.locale}-app-store-badge.svg`"
                alt="Download on the App Store"
                width="208"
              />
            </a>
          </div>
        </div>
      </v-col>

      <v-col cols="12" md="5" class="text-center">
        <p class="text-h6 mb-4">Scan with QR code</p>

        <!-- QR Code -->
        <div class="qr-code-container d-inline-block">
          <img
            src="/images/qr-codes/khamtuxa-download.png"
            alt="QR Code"
            width="280"
            height="280"
          />
        </div>
      </v-col>
    </v-row>

    <!-- Support Staff Footer -->
    <v-row
      class="pa-4 text-center d-flex justify-center"
      style="background: transparent; min-height: 10vh"
    >
      <p class="text-caption mb-0" style="color: rgba(0, 0, 0, 0.6)">
        {{ $t('payment:order:success:download:support') }}:
        <a href="tel:+84366905905" class="contact-link">+84.366.905.905</a>
        (
        <a
          href="https://zalo.me/84366905905"
          target="_blank"
          class="contact-link"
          >Zalo</a
        >,
        <a href="viber://chat?number=+84366905905" class="contact-link">Viber</a
        >,
        <a href="https://wa.me/84366905905" target="_blank" class="contact-link"
          >WhatsApp</a
        >
        )
      </p>
    </v-row>
  </v-container>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  useRoute,
  useContext
} from '@nuxtjs/composition-api'

export default defineComponent({
  layout: 'payment',
  setup() {
    const route = useRoute()
    const { i18n } = useContext()

    const hasMembershipProduct = computed(
      () => route.value.query?.membershipProduct === 'true'
    )

    return {
      hasMembershipProduct,
      i18n
    }
  }
})
</script>

<style scoped>
.max-width-container {
  max-width: 600px;
  margin: 0 auto;
}

.store-button {
  display: inline-block;
  transition: transform 0.2s;
}

.store-button img {
  max-width: 100%;
  height: auto;
  display: block;
}

.qr-code-container {
  background: white;
  border-radius: 16px;
  padding: 1rem;
}

.qr-code-container img {
  max-width: 100%;
  height: auto;
  display: block;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.gap-4 {
  gap: 16px;
}

.text-caption {
  font-size: 0.75rem;
  line-height: 1.25rem;
}

/* Add to existing styles */
.contact-link {
  color: rgba(0, 0, 0, 0.6);
  text-decoration: none;
  transition: color 0.2s ease;
}

.contact-link:hover {
  color: #00796b; /* or your theme primary color */
  text-decoration: underline;
}

@media (max-width: 960px) {
  .max-width-container {
    max-width: 100%;
  }
}

@media (max-width: 600px) {
  .text-h2 {
    font-size: 2.5rem !important;
  }

  .text-h5 {
    font-size: 1.25rem !important;
  }

  .store-button {
    display: flex;
    justify-content: center;
  }

  .store-button img {
    width: 200px;
  }

  .qr-code-container img {
    width: 200px;
    height: 200px;
  }
}
</style>
