FROM mhealthvn/node-builder:master as runner
ARG GIT_TOKEN
ENV GIT_TOKEN=$GIT_TOKEN
WORKDIR /usr/src/app
COPY package.json ./
RUN yarn cache clean && yarn --non-interactive --prod && node-prune


FROM runner as builder
WORKDIR /usr/src/app
RUN yarn
COPY . .
COPY .env.build .env
ARG FIRE_ENV
ENV FIRE_ENV=$FIRE_ENV
RUN yarn build

FROM node:16.18-alpine as final
WORKDIR /usr/src/app
COPY --from=runner /usr/src/app/node_modules node_modules
COPY --from=builder /usr/src/app/.nuxt .nuxt 
COPY . . 
RUN echo $BUILD_TAG $(date "+%F %T%z") "("$(echo $GIT_COMMIT | cut -c1-7) $GIT_BRANCH")" > ./src/static/version.txt
USER 1
CMD ["yarn", "start"]