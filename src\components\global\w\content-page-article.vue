<template>
  <div>
    <v-img
      v-if="content.featuredImage"
      :src="
        content.featuredImage.url
          ? content.featuredImage.url
          : content.featuredImage
      "
      contain
    />
    <v-container class="px-2">
      <div
        v-if="content.author"
        class="d-flex align-center font-weight-bold justify-space-between"
        style="font-size: 16px"
      >
        <div v-if="content.author" class="d-flex align-center" style="gap: 5px">
          <v-avatar v-if="content.author.avatar" size="40"
            ><v-img
              :src="
                content.author.avatar.url
                  ? content.author.avatar.url
                  : content.author.avatar
              "
          /></v-avatar>
          <div>
            {{ content.author.name }}
          </div>
        </div>
      </div>

      <div
        v-if="content.body"
        style="font-size: 16px"
        v-html="content.body"
      ></div>
    </v-container>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from '@nuxtjs/composition-api'
interface IContent {
  head?: any
  title: string
  slug?: string
  brief?: string
  body?: string
  featuredImage?: any
  author?: any
  publishedAt?: string
  inCollections?: string
  keywords?: string[]
  layout?: string
}
export default defineComponent({
  props: {
    content: {
      required: true,
      type: Object as PropType<IContent>
    }
  },
  setup(_props) {
    // load related content
  }
})
</script>
