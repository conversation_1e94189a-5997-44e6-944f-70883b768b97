import { getField, updateField } from 'vuex-map-fields';
import { GetterTree, ActionTree, MutationTree } from 'vuex'

export const state = () => ({
  locales: ['en', 'vi', 'fr'],
  locale: 'vi',
  title:"",
  site: null,
  routeTitle : {
  }
})

export type RootState = ReturnType<typeof state>

export const getters: GetterTree<RootState, RootState> = {
  getField
}

export const mutations: MutationTree<RootState> = {
  updateField,
  SET_LANG(state, locale) {
    state.locale = locale;
  },
  setSite(state, data) {
    state.site = data;
  },
  SET_TITLE(state, _title) {
    state.title = _title;
  },

}

export const actions: ActionTree<RootState, RootState> = {

}
